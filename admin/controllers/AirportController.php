<?php

namespace admin\controllers;

use common\models\SearchWord;
use Yii;
use common\models\Airport;
use common\models\AirportSearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * AirportController implements the CRUD actions for Airport model.
 */
class AirportController extends ControllerBase
{
    public function behaviorsAdditional(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
            /*[
                'class' => 'yii\filters\HttpCache',
                'only' => ['autocomplete'],
                'etagSeed' => function ($action, $params) {
                    return $_GET['term'] ?? 'none';
                },
            ],
            [
                'class' => 'yii\filters\PageCache',
                'only' => ['autocomplete'],
                'duration' => 60,
                'variations' => [
                    $_GET
                ]
            ],*/
        ];
    }

    public function actionAutocomplete($term=null, $id=null): \yii\web\Response
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out = ['results' => ['id' => '', 'text' => '']];
        if (!is_null($term)) {
            $airports = SearchWord::createQuerySearchAirport($term);
            $airports->select([
                'id' => 'airport.id',
                'text' => "CONCAT(IF(airport.type = 'airport', CONCAT(airport.city, ' - '), ''), airport.name, ' (', airport.code, ')')",
            ]);

            $out['results'] = $airports->asArray()->all();
        }
        elseif (!empty($id)) {
            $out['results'] = ['id' => $id, 'text' => Airport::findOne(['code' => $id])->name];
        }

        return $this->asJson($out);
    }

    /**
     * Lists all Airport models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new AirportSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Airport model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Airport();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Airport model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Airport model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Airport model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Airport the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Airport::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionUploader()
    {
        return $this->render('uploader');
    }
}
