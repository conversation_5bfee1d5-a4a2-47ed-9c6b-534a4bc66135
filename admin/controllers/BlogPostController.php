<?php

namespace admin\controllers;

use common\models\BlogPostTag;
use common\models\Tag;
use Yii;
use common\models\BlogPost;
use admin\models\BlogPostSearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * BlogPostController implements the CRUD actions for BlogPost model.
 */
class BlogPostController extends ControllerBase
{
    /**
     * {@inheritdoc}
     */
    public function behaviorsAdditional(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all BlogPost models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BlogPostSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'tags' => Tag::find()->select(['tag', 'id'])->column(),
        ]);
    }

    /**
     * Creates a new BlogPost model.
     * If creation is successful, the browser will be redirected to the 'update' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new BlogPost();

        if(Yii::$app->request->post()){
            $data = Yii::$app->request->post();
            $tags = $data['BlogPost']['tags'];
            unset($data['BlogPost']['tags']);

            if ($model->load($data) && $model->save()) {
                if(!empty($tags)){
                    foreach ($tags as $tagId){
                        $item = new BlogPostTag();
                        $item->post_id = $model->id;
                        $item->tag_id = $tagId;
                        $item->save();
                    }
                }

                return $this->redirect(['update', 'id' => $model->id]);
            }

        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing BlogPost model.
     * If update is successful, the browser will be redirected to the 'update' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);


        if(Yii::$app->request->post()){

            $data = Yii::$app->request->post();
            $submittedTags = $data['BlogPost']['tags'] ?? [];
            unset($data['BlogPost']['tags']);

            if ($model->load($data) && $model->save()) {
                if(!is_array($submittedTags)){
                    $submittedTags = [];
                }

                $existingTags = $model->getTags()->select('id')->column();

                $tagsToAdd = array_diff($submittedTags, $existingTags);
                $tagsToRemove = array_diff($existingTags, $submittedTags);

                foreach ($tagsToAdd as $tagId) {
                    $blogPostTag = new BlogPostTag();
                    $blogPostTag->post_id = $model->id;
                    $blogPostTag->tag_id = $tagId;
                    $blogPostTag->save();
                }

                if (!empty($tagsToRemove)) {
                    BlogPostTag::deleteAll([
                        'post_id' => $model->id,
                        'tag_id' => $tagsToRemove,
                    ]);
                }

                return $this->redirect(['update', 'id' => $model->id]);
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing BlogPost model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the BlogPost model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return BlogPost the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BlogPost::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
