<?php


namespace admin\controllers;


use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\web\Controller;

class ControllerBase extends Controller
{
    public function behaviorsAdditional(): array
    {
        return [];
    }

    public function behaviors()
    {
        return ArrayHelper::merge([
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ], $this->behaviorsAdditional());
    }

}