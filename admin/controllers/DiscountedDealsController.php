<?php

namespace admin\controllers;

use common\models\Airport;
use common\models\Promo;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use Yii;

/**
 * DiscountedDealsController implements the CRUD actions for Airport model.
 */
class DiscountedDealsController extends ControllerBase
{

    /**
     * Lists all Review models.
     * @return mixed
     */
    public function actionIndex()
    {

        $dataProvider = new ActiveDataProvider([
            'query' => Promo::find()->orderBy('created_at desc'),
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Agent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Promo();

        if (!empty(Yii::$app->request->post())) {
            $data = Yii::$app->request->post()['Promo'];

            $airport = Airport::findOne($data['place_to']);

            $promo = new Promo();
            $promo->created_at = time();
            $promo->place_to = htmlspecialchars($data['place_to']);
            $promo->city = $airport->city;
            $promo->price = (float)$data['price'];
            $promo->order = (int)$data['order'];
            $promo->image = $data['image'];

            if($promo->save()){
                return $this->redirect(['index']);
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Agent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if (!empty(Yii::$app->request->post())) {
            $data = Yii::$app->request->post()['Promo'];

            $airport = Airport::findOne($data['place_to']);

            $model->place_to = htmlspecialchars($data['place_to']);
            $model->city = $airport->city;
            $model->price = (float)$data['price'];
            $model->order = (int)$data['order'];
            $model->image = $data['image'];
            if($model->save()){
                return $this->redirect(['update', 'id' => $model->id]);
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Agent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Agent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Promo the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Promo::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
