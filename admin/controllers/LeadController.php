<?php

namespace admin\controllers;

use admin\models\LeadSearch;
use common\models\Lead;
use Yii;
use yii\data\ActiveDataProvider;

/**
 * LeadController implements the CRUD actions for Lead model.
 */
class LeadController extends ControllerBase
{

    /**
     * Lists all Review models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new LeadSearch();
        $dataProvider = new ActiveDataProvider([
            'query' => Lead::find()->orderBy('created_at desc'),
        ]);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id)
    {
        $lead = Lead::findOne(['id' => $id]);
        $lead->sendSuccessMailToSandbox();
    }
}
