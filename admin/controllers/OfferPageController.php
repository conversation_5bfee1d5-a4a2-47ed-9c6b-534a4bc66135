<?php

namespace admin\controllers;

use Yii;
use common\models\OfferPage;
use admin\models\OfferPageSearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * OfferPageController implements the CRUD actions for OfferPage model.
 */
class OfferPageController extends ControllerBase
{
    /**
     * {@inheritdoc}
     */
    public function behaviorsAdditional(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all OfferPage models.
     * @return mixed
     */
    public function actionIndex(string $class)
    {
        $searchModel = new OfferPageSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $class);

        return $this->render('index', [
            'class' => $class,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new OfferPage model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($type, string $class)
    {
        $model = new OfferPage();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
            'type' => $type,
            'class' => $class
        ]);
    }

    /**
     * Updates an existing OfferPage model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing OfferPage model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionUpdateall()
    {
        OfferPage::updateAll(['updated_at' => time()]);
        return $this->redirect(['index']);
    }

    /**
     * Finds the OfferPage model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return OfferPage the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = OfferPage::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionAutocomplete($term=null, $id=null, $class=null): \yii\web\Response
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out = ['results' => ['id' => '', 'text' => '']];
        if (!is_null($term)) {
            $offerPages = OfferPage::find()->where(['like', 'name', $term]);
            if ($class) {
                $offerPages->andWhere(['class' => $class]);
            }
            $offerPages->select([
                'id' => 'offer_page.id',
                'text' => "offer_page.name"                
            ]);

            $out['results'] = $offerPages->asArray()->all();
        }
        elseif (!empty($id)) {
            $out['results'] = ['id' => $id, 'text' => OfferPage::findOne(['id' => $id])->name];
        }

        return $this->asJson($out);
    }
}
