<?php

namespace admin\controllers;

use common\models\Review;
use yii\data\ActiveDataProvider;

/**
 * AirportController implements the CRUD actions for Airport model.
 */
class ReviewController extends ControllerBase
{

    /**
     * Lists all Review models.
     * @return mixed
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => Review::find()->orderBy('created_at desc'),
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }
}
