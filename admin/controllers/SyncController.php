<?php


namespace admin\controllers;


use common\models\CorpClient;
use common\models\Lead;
use common\models\Subscribe;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\Controller;

class SyncController extends Controller
{
    /**
     * @var string
     */
    public $defaultAction = 'sync';

    public $enableCsrfValidation = false;

    protected function buildHash(string $data, $time): string {
        $salt = env('SYNC_KEY', 'NOT_SET_KEY');
        return md5($data.$time.$salt);
    }

    public function actionSync(): \yii\web\Response
    {
        try{
            $request = Yii::$app->request;
            $data = $request->post('data', 'undefined');
            $hash = $request->post('hash');
            $utcTimeStamp = $request->post('time');

            $nowDate = new \DateTime("now", new \DateTimeZone("UTC"));
            $nowTimeStamp = $nowDate->getTimestamp();

            if(($nowTimeStamp - $utcTimeStamp) > 90) {
                throw new \Exception('Hash has expired', 403);
            }

            if($this->buildHash($data, $utcTimeStamp) !== $hash) {
                throw new \Exception('Invalid Hash', 401);
            }

            $data = json_decode($data, true);

            $action = $data['action'];
            $params = $data['params'] ?? [];

            $result = call_user_func([$this, 'endpoint' . $action], $params);
        } catch (\Exception $exception){
            Yii::$app->response->statusCode = 500;
            return $this->asJson(['status' => 0, 'error' => $exception->getMessage(), 'code' => $exception->getCode()]);
        }

        return $this->asJson(['status' => 1, 'result' => $result]);
    }


    public function endpointSendEmail(array $params): bool
    {
        $templateData = [
            'agentName' => '',
            'agentLasName' => '',
            'agentEmail' => '',
            'agentPhone' => '',
            'agentImage' => '',
            'agentPosition' => 'Agent',

            'salesPhone' => Yii::$app->params['salesPhone'],
            'salesEmail' => Yii::$app->params['salesEmail'],
        ];

        $templateData = ArrayHelper::merge($templateData, $params);

        return Yii::$app->mailer->compose($params['template'], $templateData)
            ->setTo([$params['email'] => $params['name']])
            ->setFrom([$templateData['agentEmail'] => $templateData['agentName']])
            ->setSubject($params['subject'])
            ->send();
    }

    protected function endpointTest() {
        return 1;
    }

    protected function endpointError() {
        throw new \Exception('Hello', 123321);
    }

    protected function endpointBulk() {
        $tosunc = Lead::find()->where(['can_sync'=>1, 'is_sync' => 0])->orderBy(['id' => SORT_ASC])->limit(50);

        $result = [];
        /** @var Lead $lead */
        foreach ($tosunc->each() as $lead){
            $result[] = [
                'id' => $lead->id,
                'result' => json_decode($lead->data, true)
            ];
        }
        return $result;
    }

    protected function endpointMark(array $params): int
    {
        if(!empty($params['ids'])){
            $result = (int) Lead::updateAll(['is_sync' => 1], ['id' => $params['ids']]);
            $this->cleanCustomerData();
        } else {
            $result = 0;
        }

        return $result;
    }

    protected function endpointBulkNew() {
        $tosunc = Lead::find()->where(['can_sync'=>1, 'is_sync_new' => 0])->orderBy(['id' => SORT_ASC])->limit(50);

        $result = [];
        /** @var Lead $lead */
        foreach ($tosunc->each() as $lead){
            $result[] = [
                'id' => $lead->id,
                'slug' => $lead->slug,
                'result' => json_decode($lead->data, true)
            ];
        }
        return $result;
    }

    protected function endpointMarkNew(array $params) {
        if(!empty($params['ids'])){
            $result = (int) Lead::updateAll(['is_sync_new' => 1], ['id' => $params['ids']]);
            $this->cleanCustomerData();
        } else {
            $result = 0;
        }

        return $result;
    }

    protected function endpointBulkSubscribers() {
        $tosunc = Subscribe::find()->where(['is_sync' => 0])->orderBy(['id' => SORT_ASC])->limit(50);

        $result = [];
        /** @var Subscribe $subscriber */
        foreach ($tosunc->each() as $subscriber){
            $result[] = $subscriber->attributes;
        }
        return $result;
    }

    protected function endpointMarkSubscribers(array $params): int
    {
        if(!empty($params['ids'])){
            $result = (int) Subscribe::updateAll(['is_sync' => 1], ['id' => $params['ids']]);
        } else {
            $result = 0;
        }
        return $result;
    }

    protected function endpointBulkCorpClients() {

        $tosunc = CorpClient::find()->where(['is_sync' => 0])->orderBy(['id' => SORT_ASC])->limit(50);

        $result = [];
        /** @var CorpClient $corpClient */
        foreach ($tosunc->each() as $corpClient){
            $result[] = [
                'id' => $corpClient->id,
                'fullName' => $corpClient->fullName,
                'email' => $corpClient->email,
                'phone' => $corpClient->phone,
                'company' => $corpClient->company,
                'travellers' => $corpClient->travellers,
                'created_at' => $corpClient->created_at,
            ];
        }
        return $result;
    }

    protected function endpointMarkCorpClients(array $params): int
    {
        if(!empty($params['ids'])){
            $result = (int) CorpClient::updateAll(['is_sync' => 1], ['id' => $params['ids']]);
        } else {
            $result = 0;
        }
        return $result;
    }


    protected function cleanCustomerData(){
        $query = Lead::find()->where(['is_sync' => 1, 'is_sync_new' => 1, 'is_clean' => 0]);

        /** @var Lead $lead */
        foreach ($query->each() as $lead){
            $lead->cleanCuctomerData();
        }

        Lead::deleteAll(['<', 'created_at', strtotime('-90days')]);
    }

}
