<?php

namespace admin\controllers;

use common\models\ZoneCsvImport;
use Yii;
use common\models\ZonePrice;
use common\models\ZonePriceSearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * ZonePriceController implements the CRUD actions for ZonePrice model.
 */
class ZonePriceController extends ControllerBase
{
    public function behaviorsAdditional(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function exportToCsv($prefix="", $query) {


        $fp = tmpfile();
//        var_export($file);
//        $fp = fopen($file, 'w');

//        $query = ZonePrice::find();

        fputcsv($fp, ZonePrice::csvFieldsLabels());
        /** @var ZonePrice $price */
        foreach ($query->each() as $price) {
            fputcsv($fp, $price->csvFields(), ';');
        }

        return $this->response->sendStreamAsFile($fp, $prefix.'zone_price_'.date('Y-m-d_Hi').'.csv', ['mimeType' => 'application/csv', 'inline'   => false]);
    }

    /**
     * Lists all ZonePrice models.
     * @return mixed
     */
    public function actionIndex()
    {

        $csvImport = new ZoneCsvImport();
        if($csvImport->load(Yii::$app->request->post())) {
            $csvImport->file = UploadedFile::getInstance($csvImport, 'file');
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            if($csvImport->validate() && $csvImport->run()){
                Yii::$app->session->setFlash('success', 'Success');
                return $this->refresh();
            }
        }

        $searchModel = new ZonePriceSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        if(!empty($this->request->getQueryParam('export', 0))){
            $prefix = [
                ($searchModel->from) ?: 'YYY',
                ($searchModel->to) ?: 'YYY',
                $searchModel->getTypeText(),
            ];
            return $this->exportToCsv(implode('-',$prefix).'_', $dataProvider->query);
        }


        return $this->render('index', [
            'csvImport' => $csvImport,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Creates a new ZonePrice model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ZonePrice();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'from' => $model->from, 'to' => $model->to, 'type' => $model->type]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ZonePrice model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $from
     * @param string $to
     * @param int $type
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($from, $to, $type)
    {
        $model = $this->findModel($from, $to, $type);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'from' => $model->from, 'to' => $model->to, 'type' => $model->type]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ZonePrice model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $from
     * @param string $to
     * @param int $type
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($from, $to, $type)
    {
        $this->findModel($from, $to, $type)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ZonePrice model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $from
     * @param string $to
     * @param int $type
     * @return ZonePrice the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($from, $to, $type)
    {
        if (($model = ZonePrice::findOne(['from' => $from, 'to' => $to, 'type' => $type])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
