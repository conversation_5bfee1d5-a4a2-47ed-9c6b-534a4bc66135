<?php

namespace admin\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\BestDeal;

/**
 * BestDealSearch represents the model behind the search form of `common\models\BestDeal`.
 */
class BestDealSearch extends BestDeal
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'discount_min', 'discount_max', 'created_at', 'updated_at', 'updated_by', 'created_by'], 'integer'],
            [['place_from', 'place_to', 'airline_list', 'image'], 'safe'],
            [['price'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = BestDeal::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'price' => $this->price,
            'discount_min' => $this->discount_min,
            'discount_max' => $this->discount_max,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'updated_by' => $this->updated_by,
            'created_by' => $this->created_by,
        ]);

        $query->andFilterWhere(['like', 'place_from', $this->place_from])
            ->andFilterWhere(['like', 'place_to', $this->place_to])
            ->andFilterWhere(['like', 'airline_list', $this->airline_list])
            ->andFilterWhere(['like', 'image', $this->image]);

        return $dataProvider;
    }
}
