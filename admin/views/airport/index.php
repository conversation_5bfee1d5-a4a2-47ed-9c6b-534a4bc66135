<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel common\models\AirportSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Airports';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="airport-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Create Airport', ['create'], ['class' => 'btn btn-success']) ?>
        <span class="pull-right">
            <?= Html::a('City Images', ['uploader'], ['class' => 'btn btn-primary']) ?>
        </span>
    </p>

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'code',
            'city_code',
            'name',
            'city',
            'country',
            [
                'attribute' => 'type',
                'filter' => \common\models\Airport::typeList()

            ],
            [
                'attribute' => 'is_top',
                'filter' => ['0' => 'Not', '1' => 'Yes'],
                'value' => function($model) {
                    return $model->is_top ? 'Yes' : 'No';
                }
            ],

            //'city_code',
            //'type',
            //'main_airport_name',
            //'country_code',
            //'weight',
            //'index_strings:ntext',

            ['class' => 'yii\grid\ActionColumn', 'template' => '{update} {delete}'],
        ],
    ]); ?>


</div>
