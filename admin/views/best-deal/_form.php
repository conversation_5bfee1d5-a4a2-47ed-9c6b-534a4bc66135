<?php

use common\models\BestDeal;
use mihaildev\elfinder\InputFile;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;

/* @var $this yii\web\View */
/* @var $model common\models\BestDeal */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="best-deals-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'slug')->textInput() ?>

    <?= $form->field($model, 'place_from')->widget(Select2::class, [
        'data' => [$model->place_from => $model->placeFromText],
        'options' => ['multiple'=>false, 'placeholder' => 'Search for a airport ...'],
        'pluginOptions' => [
            'allowClear' => true,
            'minimumInputLength' => 2,
            'ajax' => [
                'url' => Url::to(['/airport/autocomplete']),
                'dataType' => 'json',
                'data' => new JsExpression('function(params) { return {term:params.term}; }')
            ],
            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
            'templateResult' => new JsExpression('function(airport) { return airport.text; }'),
            'templateSelection' => new JsExpression('function (airport) { return airport.text; }'),
        ],
    ]); ?>

    <?= $form->field($model, 'place_to')->widget(Select2::class, [
        'data' => [$model->place_to => $model->placeToText],
        'options' => ['multiple'=>false, 'placeholder' => 'Search for a airport ...'],
        'pluginOptions' => [
            'allowClear' => true,
            'minimumInputLength' => 2,
            'ajax' => [
                'url' => Url::to(['/airport/autocomplete']),
                'dataType' => 'json',
                'data' => new JsExpression('function(params) { return {term:params.term}; }')
            ],
            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
            'templateResult' => new JsExpression('function(airport) { return airport.text; }'),
            'templateSelection' => new JsExpression('function (airport) { return airport.text; }'),
        ],
    ]); ?>


    <?= $form->field($model, 'price')->textInput() ?>

    <?= $form->field($model, 'discount_min')->textInput() ?>

    <?= $form->field($model, 'discount_max')->textInput() ?>

    <?= $form->field($model, 'airlineTags')->widget(Select2::class, [
        'data' => BestDeal::airlineList(),
        'options' => ['placeholder' => 'Select an airline ...', 'multiple' => true],
        'toggleAllSettings' => [
            'selectLabel' => '',
        ],
        'pluginOptions' => [
            'tags' => true,
            'maximumSelectionLength' => 3,
        ],
    ])?>

    <?= $form->field($model, 'image')->widget(InputFile::className(), [
        'language'      => Yii::$app->language,
        'controller'    => 'elfinder',
        'filter'        => 'image',
        'template'      => '<div class="input-group">{input}<span class="input-group-btn">{button}</span></div>',
        'options'       => ['class' => 'form-control', 'readonly' => true],
        'buttonOptions' => ['class' => 'btn btn-default'],
        'multiple'      => false       // возможность выбора нескольких файлов
    ]) ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
