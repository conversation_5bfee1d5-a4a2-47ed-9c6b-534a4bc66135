<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel admin\models\BestDealSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Trash Deal';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="best-deal-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Create Trash Deal', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
//        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'placeFromText',
            'placeToText',
            'price',
            //'discount_min',
            //'discount_max',
            //'airline_list',
            //'image',
            //'created_at',
            'updated_at:date',
            //'updated_by',
            //'created_by',

            ['class' => 'yii\grid\ActionColumn', 'template' => '{update} {delete}'],
        ],
    ]); ?>


</div>
