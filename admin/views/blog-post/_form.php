<?php

use kartik\datetime\DateTimePicker;
use mihaildev\elfinder\InputFile;
use mihaildev\elfinder\ElFinder;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use mihaildev\ckeditor\CKEditor;

/* @var $this yii\web\View */
/* @var $model common\models\BlogPost */
/* @var $tags common\models\Tag */
/* @var $form yii\widgets\ActiveForm */

$categorys = \common\models\BlogPostCategory::find()
    ->select(['title', 'id'])
    ->orderBy(['title' => SORT_ASC])
    ->indexBy('id')
    ->cache(60)
    ->column();

$tagsList = \common\models\Tag::find()
    ->select(['tag', 'id'])
    ->indexBy('id')
    ->column();


?>

<div class="blog-post-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'category_id')->dropDownList($categorys) ?>

    <?= $form->field($model, 'image')->widget(InputFile::className(), [
        'language'      => Yii::$app->language,
        'controller'    => 'elfinder',
        'filter'        => 'image',
        'template'      => '<div class="input-group">{input}<span class="input-group-btn">{button}</span></div>',
        'options'       => ['class' => 'form-control'],
        'buttonOptions' => ['class' => 'btn btn-default'],
        'multiple'      => false       // возможность выбора нескольких файлов
    ]) ?>

    <?= $form->field($model, 'small_text')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'full_text')->widget(CKEditor::className(),[
        'editorOptions' => ElFinder::ckeditorOptions('elfinder',[
            'preset' => 'full', //разработанны стандартные настройки basic, standard, full данную возможность не обязательно использовать
        ]),
    ]); ?>

    <?= $form->field($model, 'is_active')->checkbox() ?>
    <?= $form->field($model, 'is_featured')->checkbox() ?>

    <?= $form->field($model, 'publicAtFormatted')->widget(DateTimePicker::classname(), [
        'options' => ['placeholder' => 'Enter public at time ...'],
        'pluginOptions' => [
            'autoclose' => true
        ]
    ]) ?>

    <?= $form->field($model, 'tags')->widget(\kartik\select2\Select2::classname(), [
        'data' => $tagsList,
        'options' => ['placeholder' => 'Select tags...', 'multiple' => true],
        'pluginOptions' => [
            'allowClear' => true
        ],
        'value' => $model->tags,
    ]); ?>



    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
