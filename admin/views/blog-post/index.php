<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel app\models\BlogPostSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Blog Posts';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="blog-post-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Create Blog Post', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'id',
            'slug',
            'title',
            'image',
            'small_text',
            [
                'label' => 'Tags',
                'attribute' => 'tags',
                'value' => function ($model) {
                    return implode(', ', array_map(function ($tag) {
                        return $tag->tag;
                    }, $model->tags));
                },
                'format' => 'raw',
            ],
            [
                'label' => 'Is featured',
                'attribute' => 'is_featured',
                'value' => function ($model) {
                    return ($model->is_featured) ? 'Yes' : 'No';
                }
            ],
            ['class' => 'yii\grid\ActionColumn', 'template' => '{update} {delete}'],
        ],
    ]); ?>


</div>
