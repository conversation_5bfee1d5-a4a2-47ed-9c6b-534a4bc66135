<?php

/* @var $this \yii\web\View */
/* @var $content string */

use admin\assets\AppAsset;
use yii\helpers\Html;
use yii\bootstrap\Nav;
use yii\bootstrap\NavBar;
use yii\widgets\Breadcrumbs;
use common\widgets\Alert;

AppAsset::register($this);
?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>
</head>
<body>
<?php $this->beginBody() ?>

<div class="wrap">
    <?php
    NavBar::begin([
        'brandLabel' => Yii::$app->name,
        'brandUrl' => Yii::$app->homeUrl,
        'options' => [
            'class' => 'navbar-inverse navbar-fixed-top',
        ],
    ]);
    $menuItems = [];
    if (Yii::$app->user->isGuest) {
        $menuItems[] = ['label' => 'Login', 'url' => ['/site/login']];
    } else {
        $menuItems = [
            ['label' => 'Home', 'url' => ['/site/index']],
            [
                'label' => 'Blog',
                'items' => [
                    ['label' => 'Blog Posts', 'url' => ['/blog-post/index'],],
                    ['label' => 'Blog Category', 'url' => ['/blog-post-category/index']],
                    ['label' => 'Blog Tag', 'url' => ['/blog-post-tag/index']],
                ]

            ],
            [
                'label' => 'SEO',
                'items' => [
                    ['label' => 'SEO Category', 'url' => ['/seo-category/index'],],
                    ['label' => 'SEO Page', 'url' => ['/seo-page/index']],
                    ['label' => 'SEO Not Found Url', 'url' => ['/not-found-url/index']],
                ]

            ],
            ['label' => 'Leads', 'url' => ['/lead/index']],
            ['label' => 'Zone Price', 'url' => ['/zone-price/index']],
            [
                'label' => 'Deals',
                'items' => [
                    ['label' => 'Trash Deals', 'url' => ['/best-deal/index'],],
                    ['label' => 'Best Deals (Business)', 'url' => ['/offer-page/index', 'class' => 'business']],
                    ['label' => 'Best Deals (First)', 'url' => ['/offer-page/index', 'class' => 'first']],
                ]
            ],
            ['label' => 'Airport', 'url' => ['/airport/index']],
            ['label' => 'Agent', 'url' => ['/agent/index']],
            ['label' => 'Reviews', 'url' => ['/review/index']],
            ['label' => 'Promo', 'url' => ['/discounted-deals/index']],
            ['label' => 'Corporate clients', 'url' => ['/corp-clients/index']],
        ];
        $menuItems[] = '<li>'
            . Html::beginForm(['/site/logout'], 'post')
            . Html::submitButton(
                'Logout (' . Yii::$app->user->identity->username . ')',
                ['class' => 'btn btn-link logout']
            )
            . Html::endForm()
            . '</li>';
    }
    echo Nav::widget([
        'options' => ['class' => 'navbar-nav navbar-right'],
        'items' => $menuItems,
    ]);
    NavBar::end();
    ?>

    <div class="container">
        <?= Breadcrumbs::widget([
            'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
        ]) ?>
        <?= Alert::widget() ?>
        <?= $content ?>
    </div>
</div>

<footer class="footer">
    <div class="container">
        <p class="pull-left">&copy; <?= Html::encode(Yii::$app->name) ?> <?= date('Y') ?></p>

        <p class="pull-right"><?= Yii::powered() ?></p>
    </div>
</footer>

<?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>
