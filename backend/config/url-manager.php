<?php

return [
	'class' => 'yii\web\UrlManager',
    'baseUrl' => '@backend_www',
    'scriptUrl' => '@backend_www',
	'enablePrettyUrl' => true,
    'showScriptName' => false,
//    'hostInfo' => 'https://'.$_SERVER['SERVER_NAME'],
    'rules' => [
        'offer-page/index' => 'undefined',
        [
            'pattern' => 'feed/<a>',
            'route' => 'feed/<a>',
            'suffix' => '.csv'
        ],

        [
            'pattern' => 'sitemap',
            'route' => 'sitemap/generate/index',
            'suffix' => '.xml'
        ],
        [
            'pattern' => 'sitemap/<id>/sitemap_<lastmod>_<page>',
            'route' => 'sitemap/generate/show',
            'suffix' => '.xml'
        ],
        [
            'pattern' => 'track/<id>/<url:.*>',
            'route' => 'track/index',
        ],

        'blog/<category>' => 'blog-post/index',
        [
            'pattern' => 'blog/<category>',
            'route' => 'blog-post/index',
            'defaults' => ['page' => 1, 'pageSize' => 6],
        ],
        [
            'pattern' => 'blog',
            'route' => 'blog-post/index',
            'defaults' => ['page' => 1],
        ],


        'blog/page/<page:\d>' => 'blog-post/index',
        'blog/post/<slug>' => 'blog-post/view',
        'blog' => 'blog-post/index',


        [
            'pattern' => 'best-deal',
            'route' => 'best-deal/index',
            'defaults' => ['page' => 1],
        ],

//        'best-deals' => 'offer-page/index',
        [
            'pattern' => 'first-class-flights',
            'route' => 'offer-page/index',
            'defaults' => ['class' => 'first'],
        ],

        [
            'pattern' => 'business-class/airlines',
            'route' => 'offer-page/index',
            'defaults' => ['class' => 'business', 'type' => 'airline'],
        ],

        [
            'pattern' => 'best-deals',
            'route' => 'offer-page/index',
            'defaults' => ['class' => 'business'],
        ],

        'best-deal/page/<page:\d>' => 'best-deal/index',
        'best-deal' => 'best-deal/index',
        'best-deal/complete' => 'best-deal/complete',
        'best-deal/<slug>' => 'best-deal/view',

        [
            'pattern' => 'best-deals/<type>/<slug>',
            'route' => 'offer-page/view',
            'defaults' => ['class' => 'business'],
        ],
        '<class>-class/<type>/first-class-to-<slug>' => 'offer-page/view',

        [
            'pattern' => '<slug>-<class>-class',
            'route' => 'offer-page/view',
            'defaults' => ['type' => 'airline'],
        ],
        [
            'pattern' => '<class>-class/airlines',
            'route' => 'offer-page/index',
            'defaults' => ['type' => 'airline'],
        ],

        'api/deals/best' => 'offer/best-deals',

        '/' => 'site/index',
        'about' => 'site/about',
        'contact-us' => 'site/contact',
        'token/get' => 'site/token',
        'offer/search2' => 'offer/search2',
        'offer/search-price' => 'offer/search-price',
        'offer/add' => 'offer/add',
        'offer/test' => 'offer/test',
        [
            'pattern' => 'offer/index',
            'route' => 'offer/index',
            'mode' => 1
        ],
        'offer/<slug>' => 'offer/index',
        'offer/<a>/<slug>' => 'offer/<a>',

        'page/<name>' => 'site/page',

        'api/deal/best' => 'api/deal-best',
        'discounted-business-class-deals' => 'discounted-deals',
        'discounted-business-class-deals/complete' => 'discounted-deals/complete',
        'corp-clients' => 'corp-clients',
    ]
];
