<?php

namespace backend\controllers;
use backend\models\CorpClientForm;
use common\models\CorpClient;;

/**
 * Review controller
 */
class CorpClientsController extends BaseController
{

    protected bool $cacheActionsEnable = false;

    public function actionIndex()
    {
       return $this->render('corp-clients', [

       ]);
    }

    public function actionCreate()
    {
        /**
         * {
         * "fullName": "das dasdasd",
         * "email": "<EMAIL>",
         * "phone": "3424234",
         * "company": "asd",
         * "travellers": "1"
         * }
        */


        $model = new CorpClientForm();

        if ($model->load($this->request->post())) {
            $model->save();
            return $this->as<PERSON>son([
                'text' => 'ok'
            ]);
        }

        return $this->as<PERSON>son([
            'text' => '-'
        ]);
    }

    public function actionSuccess()
    {
        return $this->render('success', [
        ]);
    }

}
