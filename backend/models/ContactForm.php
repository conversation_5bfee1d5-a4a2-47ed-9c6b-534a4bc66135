<?php

namespace backend\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class ContactForm extends Model
{
    public $name;
    public $email;
    public $phone;
    public $message;

    public function formName()
    {
        return '';
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            // name, email, subject and body are required
            [['name', 'email', 'message', 'phone'], 'required'],
            // email has to be a valid email address
            ['email', 'email'],
            ['name', 'validateName'],
//            ['email', 'integer'],
        ];
    }

    public function validateName($attribute, $params, $validator)
    {
        if (!\preg_match("#^([a-z.'-]+ ?){1,5}$#i", $this->$attribute)) {
            $this->addError($attribute, 'Invalid Name.');
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'name' => 'Name',
            'email' => 'E-mail',
            'message' => 'Message',
            'phone' => 'Phone number',
        ];
    }

    /**
     * Sends an email to the specified email address using the information collected by this model.
     *
     * @return bool whether the email was sent
     */
    public function sendEmail(): bool
    {
        return Yii::$app->mailer->compose()
            ->setTo(Yii::$app->params['supportEmail'])
            ->setFrom([Yii::$app->params['senderEmail'] => Yii::$app->params['senderName']])
            ->setCc([
                Yii::$app->params['superEmail'] => Yii::$app->params['superName'],
                Yii::$app->params['salesEmail'] => Yii::$app->params['salesName']
            ])
            ->setReplyTo([$this->email => $this->name])
            ->setSubject(Yii::$app->params['project'].' Contact Form - '.$this->name)
            ->setTextBody("You have received a new message from the contact form on your website.".PHP_EOL.PHP_EOL
                ."Details:".PHP_EOL
                ." . Name: ".$this->name.PHP_EOL
                ." . E-mail: ".$this->email.PHP_EOL
                ." . Phone Number: ".$this->phone.PHP_EOL
                ." . Message: ".$this->message.PHP_EOL.PHP_EOL
                ."Please reach out to the sender as soon as possible."
            )->send();
    }
}
