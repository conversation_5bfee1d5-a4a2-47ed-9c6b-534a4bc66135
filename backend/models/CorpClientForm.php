<?php


namespace backend\models;


use common\models\CorpClient;
use common\models\Subscribe;
use yii\base\Model;

class CorpClientForm extends Model
{
    public $fullName;
    public $email;
    public $phone;
    public $company;
    public $travellers;

    public function formName()
    {
        return '';
    }

    public function rules()
    {
        return [
            [['fullName', 'email','phone','company','travellers'], 'required'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'fullName' => 'Name',
            'email' => 'E-mail',
            'phone' => 'Phone',
            'company' => 'company',
            'travellers' => 'travellers',
        ];
    }

    public function save(): bool
    {
        $model = new CorpClient();
        $model->fullName = $this->fullName;
        $model->email = $this->email;
        $model->phone = $this->phone;
        $model->company = $this->company;
        $model->travellers = $this->travellers;
        $model->is_sync = 0;
        return $model->save();
    }
}
