<?php


use backend\assets\BuildCorpClientsAsset;
use backend\components\SubscribeWidget;
use backend\components\OfferPageWidget;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\View;
use common\models\LeadDataForm;
use backend\components\NavWidget;


/* @var $this \yii\web\View */
/* @var $model \common\models\Promo */


$navItems = [
    [
        'label' => 'Home',
        'url' => ['/site/index'],
    ],
    [
        'label' => 'Blog',
        'url' => ['/blog-post/index'],
    ],
    [
        'label' => 'About us',
        'url' => ['/site/about'],
    ],
    [
        'label' => 'Best Deals',
        'url' => ['/offer-page/index', 'class' => 'business'],
        'children' => [
            [
                'label' => 'Business class',
                'url' => ['/offer-page/index', 'class' => 'business']
            ],
            [
                'label' => 'First class',
                'url' => ['/offer-page/index', 'class' => 'first']
            ]
        ]
    ],
    [
        'label' => 'Airlines',
        'url' => ['business-class/airlines'],
        'route' => 'business-class/airlines'
    ],
    [
            'label' => 'Contacts',
            'url' => ['/site/contact'],
    ]
];

$widget = OfferPageWidget::begin([]);

BuildCorpClientsAsset::register($this);

$bestDeals = $widget->run();

$this->params['header_template'] = '//layouts/header/discounted';

?>

<?php ob_start()?>
    window.corpClients = <?= Json::encode(Url::to(['/corp-clients']))?>;
<?php

$jsData = ob_get_clean();
$this->registerJs($jsData, View::POS_HEAD, 'discounted_deals_js_data');
?>
<section>
    <header class="w-full py-4 lg:pt-8 lg:pb-8 z-10">
        <div class="flex justify-between items-center px-5 lg:px-8">
            <div class="logo -mt-1 lg:mt-auto 2xl:flex-1">
                <a href="./" class="text-white flex items-end">
                    <span class="sr-only">TravelBusinessClass.com</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="79" height="24" viewBox="0 0 79 24" fill="none">
                            <g filter="url(#filter0_d_2258_32)">
                                <path d="M25.9565 23.0323L24.9279 19.5806C24.2851 19.9032 23.7387 20.0323 22.9995 20.0323C21.6496 20.0323 21.0067 19.2903 21.0067 17.9677V10.9677H25.7636V7.22581H21.0067V1.93548H16.732V7.22581H14V10.9677H16.732V17.9677C16.732 21.4839 18.4355 23.8065 22.1638 23.8065C23.5137 23.8065 24.6708 23.5484 25.9565 23.0323Z" fill="url(#paint0_linear_2258_32)"/>
                                <path d="M38.2416 6.77419C35.9275 6.77419 34.1597 7.70968 32.9705 9.19355V0H28.7279V23.5484H32.9705V21.5484C34.1597 23.0323 35.9275 24 38.2416 24C42.7413 24 46.2447 20.3226 46.2447 15.3871C46.2447 10.4194 42.7413 6.77419 38.2416 6.77419ZM37.5024 20.1935C34.9311 20.1935 32.8741 18.1935 32.8741 15.3548C32.8741 12.5484 34.9311 10.5806 37.5024 10.5806C40.1701 10.5806 42.0664 12.5484 42.0664 15.3548C42.0664 18.1935 40.1701 20.1935 37.5024 20.1935Z" fill="url(#paint1_linear_2258_32)"/>
                                <path d="M57.0933 24C61.5609 24 64.3572 21.1613 65 18.6774L61.0467 17.5806C60.6931 18.9355 59.1825 20.1935 57.0612 20.1935C54.3613 20.1935 52.6257 17.9032 52.6257 15.3548C52.6257 12.7742 54.3613 10.5484 57.0612 10.5484C59.1825 10.5484 60.6931 11.9032 61.0467 13.2581L65 12.129C64.3572 9.67742 61.5609 6.77419 57.0612 6.77419C52.1115 6.77419 48.4474 10.4839 48.4474 15.3548C48.4474 20.2581 52.1115 24 57.0933 24Z" fill="url(#paint2_linear_2258_32)"/>
                            </g>
                            <defs>
                                <filter id="filter0_d_2258_32" x="0" y="0" width="79" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="14"/>
                                <feGaussianBlur stdDeviation="7"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2258_32"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2258_32" result="shape"/>
                                </filter>
                                <linearGradient id="paint0_linear_2258_32" x1="65" y1="-1.43051e-06" x2="29.5957" y2="38.3848" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#590C32"/>
                                <stop offset="1" stop-color="#9D1D5A"/>
                                </linearGradient>
                                <linearGradient id="paint1_linear_2258_32" x1="65" y1="-1.43051e-06" x2="29.5957" y2="38.3848" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#590C32"/>
                                <stop offset="1" stop-color="#9D1D5A"/>
                                </linearGradient>
                                <linearGradient id="paint2_linear_2258_32" x1="65" y1="-1.43051e-06" x2="29.5957" y2="38.3848" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#590C32"/>
                                <stop offset="1" stop-color="#9D1D5A"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </a>
                </div>

                <div class="text-center lg:hidden">
                    <p class="font-normal text-xs">TravelBusinessClass.com</p>
                    <a href="tel:+***********" class="font-bold block leading-none text-sm">+****************</a>
                </div>

                <div class="-mr-2 lg:hidden">
                    <button type="button" aria-label="Menu" class="inline-flex items-center justify-center p-2" @click="slideOverOpen = true">
                        <svg class="w-8 h-8 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
                            <path d="M2 8a2 2 0 012-2h24a2 2 0 110 4H4a2 2 0 01-2-2zm0 8a2 2 0 012-2h24a2 2 0 110 4H4a2 2 0 01-2-2zm2 6a2 2 0 100 4h5a2 2 0 100-4H4z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>

                <?=NavWidget::widget([
                 'options' => ['class' => 'nav hidden lg:flex lg:gap-x-10 lg:items-center 2xl:flex-1 2xl:text-center'],
                 'linkOptions' => ['class' => 'nav__link'],
                 'activeClass' => '--active',
                 'items' => $navItems,
                 ])?>

                 <div class="hidden lg:flex lg:items-center lg:justify-end 2xl:flex-1">
                     <div class="hidden text-right mr-4 xl:block">
                         <span class="text-sm text-[#71777D]">Call us 24/7</span>
                         <a href="tel:+***********" class="flex items-center font-bold text-lg">
                             <svg class="w-4 fill-current mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                 <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                             </svg>+****************
                         </a>
                     </div>
                    <button type="button" aria-label="Call us" class="group bg-white hover:bg-gradient-to-r from-pink-light to-pink-dark rounded-full flex items-center justify-center w-14 h-14 shadow-lg focus:outline-none">
                        <svg class="w-6 h-6 fill-gradient group-hover:fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M20.909 11.808c.408-2.395-.596-4.833-2.188-6.514-1.587-1.676-3.899-2.738-6.2-2.113l.376 1.372c1.627-.442 3.425.28 4.785 1.717 1.356 1.432 2.136 3.438 1.819 5.3l1.408.238z"></path>
                            <path d="M2 7.631a4.128 4.128 0 014.136-4.12c.446 0 .877.071 1.281.202.517.168.82.615.899 1.06l.703 4.015.122.856c.063.44-.08.948-.505 1.269a4.13 4.13 0 01-1.502.717 10.08 10.08 0 002.135 3.13c.904.9 1.971 1.622 3.144 2.127a4.1 4.1 0 01.72-1.496c.322-.422.832-.565 1.274-.502l.857.121 4.033.7c.447.078.896.38 1.064.895.132.403.203.832.203 1.276a4.128 4.128 0 01-4.678 4.084 14.459 14.459 0 01-4.971-1.082 14.406 14.406 0 01-4.672-3.11A14.342 14.342 0 012.34 10.62a14.29 14.29 0 01-.306-2.453A4.136 4.136 0 012 7.631zm13.03 8.782l-.797-.113c-.208.286-.36.614-.443.967-.085.364-.31.681-.615.878-.315.203-.743.29-1.169.114a11.552 11.552 0 01-3.747-2.494 11.5 11.5 0 01-2.504-3.732 1.242 1.242 0 01.115-1.162 1.44 1.44 0 01.88-.612c.356-.082.686-.235.973-.442l-.118-.823-.632-3.613-.059-.334a2.718 2.718 0 00-3.398 1.898 2.691 2.691 0 00-.062 1.063l.005.036.001.037a12.875 12.875 0 00.98 4.495 12.92 12.92 0 002.812 4.192 12.978 12.978 0 006.471 3.502c.739.156 1.491.249 2.249.275l.037.001.037.006a2.703 2.703 0 002.976-3.445l-3.992-.694z"></path>
                            <path d="M16.67 7.739c.918.967 1.399 2.397 1.012 3.785l-1.376-.38c.234-.84-.047-1.768-.674-2.428-.616-.65-1.517-.987-2.487-.72l-.38-1.37c1.547-.426 2.976.135 3.905 1.113z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>
</section>
<section class="w-full pb-2 px-2 hd:px-4 hd:pb-0">
    <div class="flex flex-col lg:grid lg:grid-cols-2 gap-2 hd:gap-x-4">
        <div class="pt-8 px-4 pb-6 lg:px-10 lg:pb-8 hd:px-24 hd:pb-14 bg-[rgba(255,255,255,0.4)] rounded-[32px]" style="box-shadow: 0 45px 100px 0 rgba(0, 0, 0, 0.05);">
            <div class="h-full flex flex-col">
                <div class="flex flex-col items-center lg:items-start gap-y-6 lg:gap-y-10 mt-auto">
                    <div class="flex flex-col items-center lg:items-start gap-y-4">
                        <div class="bg-white p-1 pr-2 lg:pr-4 w-[fit-content] rounded-full flex items-center gap-x-1 lg:gap-x-2">
                            <span class="tracking-[0.9px] text-[10px] font-bold text-gradient uppercase py-1.5 px-2 lg:px-3 border border-[#F4F6F6] rounded-full leading-[120%]">Smart Deal</span>
                            <span class="tracking-[0.9px] text-[10px] font-bold uppercase text-[#282F36] leading-[120%]">Global trust, seamless travel</span>
                        </div>
                        <div class="flex flex-col gap-y-6 max-w-[460px] hd:max-w-[675px]">
                            <h1 class="text-4xl font-bold leading-[130%] lg:pl-0 hd:text-[56px]">Business Class Travel for Teams, Made Simple</h1>
                            <div>
                                <ul class="list-disc pl-5 lg:pl-6 space-y-2 lg:space-y-3 marker:text-[rgb(214,219,220)]">
                                    <li class="hd:text-lg text-base"><span class="font-bold text-gradient">Save up to 60%</span> on premium flights for your team.</li>
                                    <li class="hd:text-lg text-base">Dedicated managers.</li>
                                    <li class="hd:text-lg text-base">Zero hassle.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <button class="get-corporate-offer btn btn--gradient w-[fit-content] flex items-center gap-x-4 max-h-[48px] lg:max-h-unset pr-4 h-[46px] lg:py-7 hd:py-8" >
                        <span class="text-xs">Get Your Corporate Offer</span>
                        <svg class="w-4 h-4 fill-current text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M6.282 2.304a1 1 0 011.414-.022l9.273 9a1 1 0 010 1.436l-9.273 9a1 1 0 01-1.392-1.436L14.837 12 6.304 3.718a1 1 0 01-.022-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
                <div class="pt-6 lg:pt-[5rem] hd:pt-[6.5rem] flex flex-col lg:flex-row gap-3 items-center">
                    <div class="flex justify-center -space-x-4">
                        <img src="./build/img/static/corpClients/female-avatar-6.png" alt="Avatar 2" class="h-10 w-10 hd:w-12 hd:h-12 rounded-full object-cover border-white" />
                        <img src="./build/img/static/corpClients/female-avatar-5.png" alt="Avatar 1" class="h-10 w-10 hd:w-12 hd:h-12 rounded-full object-cover border-white" />
                        <img src="./build/img/static/corpClients/male-avatar-2.png" alt="Avatar 6" class="h-10 w-10 hd:w-12 hd:h-12 rounded-full object-cover border-white" />
                    </div>
                    <div class="flex flex-col gap-y-1">
                        <div class="flex items-center gap-x-1.5 justify-center lg:justify-start">
                            <a
                                href="https://trustpilot.com/review/travelbusinessclass.com?utm_medium=Trustbox&utm_source=EmailSignature2"
                                target="_blank"
                                rel="noopener noreferrer"
                                class="flex flex-col gap-y-1"
                            >
                                <div class="flex items-center gap-x-2">
                                    <svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M-0.00134087 9.1713H9.64434L12.6136 0L15.6004 9.1713L25.2461 9.15373L17.4452 14.8287L20.432 24L12.6312 18.325L4.83028 24L7.79954 14.8287L-0.00134087 9.1713Z" fill="#00B67A"/>
                                        <path d="M7.13158 16.9018L7.79923 14.8286L12.6133 18.325L7.13158 16.9018Z" fill="#005128"/>
                                    </svg>
                                    <img
                                        class="mt-1"
                                        :src="`https://emailsignature.trustpilot.com/signature/en-GB/2/5fb544c7c119fd0001c9e8a2/text.png`"
                                        width="164"
                                        style="border: none;"
                                        alt="Trustpilot rating"
                                    >
                                </div>
                                <span class="text-[#71777D] text-sm hd:text-base">500+ Companies already joined us!</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="min-h-[400px] rounded-[32px] lg:min-h-[640px] hd:min-h-[745px] w-full bg-cover bg-no-repeat" style="background-image: url(./build/img/static/corpClients/main.jpg); background-position: center 30%;">
        </div>
    </div>
</section>
<section class="sm:max-w-[1475px] px-2 mx-auto pt-18 lg:pt-16 pb-10 lg:px-10 hd:pt-24 hd:pb-16 hd:px-0">
    <div class="w-full flex flex-col gap-y-10 hd:gap-y-14">
        <div class="max-w-[400px] hd:max-w-[650px] text-center mx-auto flex flex-col gap-y-2 hd:gap-y-4">
            <span class="tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Benefits</span>
            <h2 class="text-2xl hd:text-4xl font-bold !leading-[130%]">Why Choose Us for Corporate Travel?</h2>
        </div>
        <div class="w-full flex flex-col gap-y-10 lg:grid lg:grid-cols-2 gap-x-6 hd:gap-x-4">
            <div class="max-w-[330px] mx-auto sm:max-w-none w-full lg:py-4 flex flex-col items-center justify-center sm:grid sm:grid-cols-2 lg:items-start gap-6">
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/star.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%]">Priority Booking & Seamless Upgrades</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Secure your seats faster and enjoy smooth upgrades with exclusive privileges.</span>
                    </div>
                </div>
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/plane.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%]">Customized Rates with 50+ Airlines</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Enjoy exclusive corporate pricing across major global carriers.</span>
                    </div>
                </div>
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/mouse-pointer.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%]">Flexible Booking Conditions</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Adapt your travel plans effortlessly with our policies.</span>
                    </div>
                </div>
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/file-chart.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%] lg:max-w-[212px] hd:max-w-none">Accounting-Friendly Reporting</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Seamless integration with your financial records and reports.</span>
                    </div>
                </div>
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/coins.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%]">Loyalty Programs & Cashback</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Earn rewards and cashback on every corporate booking.</span>
                    </div>
                </div>
                <div class="max-w-[315px] sm:mx-auto lg:mx-0 lg:max-w-none flex flex-col items-center text-center lg:items-start lg:text-left gap-y-4">
                    <div class="bg-white p-3.5 lg:p-3 hd:p-3.5 rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/headset.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <span class="text-base lg:text-sm hd:text-base font-bold text-[#282F36] leading-[130%]">24/7 Personal Manager</span>
                        <span class="text-base lg:text-sm hd:text-base text-[#71777D] leading-[150%]">Get dedicated support at any time, wherever you are.</span>
                    </div>
                </div>
            </div>
            <div class="min-h-[400px] w-full rounded-3xl bg-cover bg-no-repeat" style="background-image: url(./build/img/static/corpClients/benefits.jpg); background-position: center 30%;"></div>
        </div>
    </div>
</section>
<section class="sm:max-w-[1475px] px-2 mx-auto pt-18 lg:pt-16 pb-10 lg:px-10 hd:pt-24 hd:pb-16 hd:px-0">
    <div class="flex flex-col gap-y-12">
        <span class="max-w-[330px] text-center sm:max-w-none tracking-[1.2px] uppercase text-[#71777D] font-bold text-xs hd:text-sm mx-auto">Trusted by <span class="text-gradient">Leading Corporations</span> Worldwide</span>
        <div class="max-w-[945px] lg:w-full lg:mx-auto">
            <div class="text-center" id="corp-partners"></div>
                <div class="swiper-container corp-partners-carousel">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[87px] lg:w-[122px] hd:w-[131px]" src="./build/img/static/corpClients/layersLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[105px] lg:w-[140px] hd:w-[157px]" src="./build/img/static/corpClients/sisyphusLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[101px] lg:w-[133px] hd:w-[152px]" src="./build/img/static/corpClients/catalogLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[105px] lg:w-[140px] hd:w-[157px]" src="./build/img/static/corpClients/quxtientLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[113px] lg:w-[155px] hd:w-[170px]" src="./build/img/static/corpClients/hourglassLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[125px] lg:w-[165px] hd:w-[180px]" src="./build/img/static/corpClients/alphaWaveLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[105px] lg:w-[140px] hd:w-[158px]" src="./build/img/static/corpClients/easyTaxLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[131px] lg:w-[175px] hd:w-[197px]" src="./build/img/static/corpClients/lkigaiLabsLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[110px] lg:w-[150px] hd:w-[165px]" src="./build/img/static/corpClients/peregrinLogo.svg" alt="">
                        </div>
                        <div class="swiper-slide flex items-center justify-center swiper-slide-reset-before w-full md:w-1/2 xl:w-1/3 2xl:w-1/5" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners">
                            <img class="w-[107px] lg:w-[145px] hd:w-[160px]" src="./build/img/static/corpClients/spheruleLogo.svg" alt="">
                        </div>
                    </div>
                    <div class="corp-partners-carousel-pagination swiper-pagination" data-aos="fade-in" data-aos-duration="1200" data-aos-delay="900" data-aos-offset="300" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-partners"></div>
                </div>
        </div>
    </div>
</section>
<section class="max-w-[1475px] mx-auto px-2 hd:px-0 pt-16 pb-8 lg:py-10 hd:py-16">
    <div class="w-full rounded-3xl bg-[#061620] flex flex-col lg:flex-row gap-x-12 gap-y-10 justify-center p-2 pb-6 lg:p-10 hd:py-14">
        <div class="h-[370px] lg:w-[260px] hd:w-[350px] rounded-3xl bg-cover bg-center bg-no-repeat" style="background-image: url(./build/img/static/corpClients/review.jpg);"></div>
        <div class="flex flex-col sm:mx-auto lg:mx-0 gap-y-6 max-w-[575px] lg:max-w-[620px]">
            <div class="flex flex-col gap-y-7">
                <span class="text-lg text-white text-center px-2 lg:px-0 lg:text-left leading-[150%]">“Our partnership with TravelBusinessClass allowed us to cut travel expenses by 20%, making business trips more accessible for our team. Their exclusive corporate rates and seamless booking process have transformed the way we manage travel.”</span>
                <div class="flex gap-x-6 justify-center lg:justify-start items-center">
                    <div class="flex flex-col gap-y-0.5">
                        <span class="text-white text-sm leading-[150%]">Sophia Lee</span>
                        <span class="text-[#F4F6F6] text-xs leading-[150%]">CEO, Circooles Inc.</span>
                    </div>
                    <img src="./build/img/static/corpClients/circoolesLogoWhite.png" alt="">
                </div>
            </div>
            <div class="max-w-[350px] w-full mx-auto px-2 lg:px-0 sm:mx-0 sm:max-w-none h-full">
                <div class="flex items-center justify-between border-t pt-6 lg:pt-0 h-full">
                    <div class="flex flex-col sm:flex-row gap-2 text-center sm:text-start items-center">
                        <span class="text-2xl sm:text-4xl text-white leading-[130%] font-bold">
                            20%
                        </span>
                        <span class="text-[#838D95] text-xs leading-[150%] max-w-[80px] lg:text-left">
                            Reduction in Travel Costs
                        </span>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-2 text-center sm:text-start items-center">
                        <span class="text-2xl sm:text-4xl text-white leading-[130%] font-bold">
                            100%
                        </span>
                        <span class="text-[#838D95] text-xs leading-[150%] max-w-[80px] lg:text-left">
                            Satisfaction Rate
                        </span>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-2 text-center sm:text-start items-center">
                        <span class="text-2xl sm:text-4xl text-white leading-[130%] font-bold">
                            24/7
                        </span>
                        <span class="text-[#838D95] text-xs leading-[150%] max-w-[80px] lg:text-left">
                            Customer Support
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="max-w-[600px] lg:max-w-[1475px] mx-auto px-2 pb-2 pt-20 lg:pt-16 lg:pb-10 hd:pt-24 hd:pb-16 hd:px-0">
    <div class="w-full flex flex-col gap-y-10 hd:gap-y-14">
        <div class="max-w-[420px] hd:max-w-[600px] text-center mx-auto flex flex-col gap-y-2 hd:gap-y-4">
            <span class="tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Get Your Corporate Travel Offer</span>
            <h2 class="text-2xl hd:text-4xl font-bold leading-[130%]">Unlock exclusive corporate rates for your team.</h2>
        </div>
        <div class="flex flex-col gap-y-2 lg:grid lg:grid-cols-2 gap-x-2 hd:gap-x-4">
            <div class="min-h-[400px] lg:min-h-none h-full w-full rounded-3xl bg-cover bg-center bg-no-repeat" style="background-image: url(./build/img/static/corpClients/travel-offer.jpg);"></div>
            <div class="rounded-3xl w-full bg-white pt-8 pb-6 px-4 lg:p-8 lg:pt-10 hd:px-20 hd:pt-10 hd:pb-8 flex flex-col gap-y-8 hd:gap-y-[32px] items-center" id="formBlock">
                <div class="flex flex-col items-center w-full gap-y-6">
                    <div class="p-4 rounded-full bg-white w-[fit-content] shadow-lg">
                        <img class="hd:w-7 hd:h-7 w-6 h-6" src="./build/img/static/corpClients/plane-flight.svg" alt="">
                    </div>
                    <h4 class="text-lg hd:text-2xl font-bold leading-[130%] max-w-[296px] lg:max-w-[550px] text-center">Submit your details and receive a personalized offer within 24 hours.</h4>
                </div>
                <div class="w-full">
                    <form action="#" autocomplete="off" id="form-container">
                      <div class="flex flex-col gap-y-4">
                        <!-- Name -->
                        <div class="form-field border border-[#E8EBEC] rounded-lg mt-1">
                          <input id="fullName" name="fullName" type="text" class="form-control" placeholder="John Doe" autocomplete="name" />
                          <label for="fullName" class="form-label">Enter your Name and Surname</label>
                        </div>

                        <!-- Email -->
                        <div class="form-field border border-[#E8EBEC] rounded-lg mt-1">
                          <input id="email" name="email" type="email" class="form-control" placeholder="<EMAIL>" autocomplete="email" />
                          <label for="email" class="form-label">Enter your Email</label>
                        </div>

                        <!-- Phone -->
                        <div class="form-field border border-[#E8EBEC] rounded-lg mt-1">
                          <input id="phone" name="phone" type="tel" class="form-control border-0 js-phone" data-class="iti--transparent text-sm" placeholder="(888) 12 4567" autocomplete="tel" />
                          <label for="phone" class="form-label">Enter your phone</label>
                        </div>

                        <!-- Company + Travellers -->
                        <div class="grid grid-cols-2 gap-x-4">
                          <div class="form-field border border-[#E8EBEC] rounded-lg mt-1">
                            <input id="company" name="company" type="text" class="form-control" placeholder="Company name" />
                            <label for="company" class="form-label">Company</label>
                          </div>
                          <div class="form-field border border-[#E8EBEC] rounded-lg mt-1">
                            <input id="travellers" name="travellers" type="number" min="1" class="form-control" placeholder="10" />
                            <label for="travellers" class="form-label">Number of Travellers</label>
                          </div>
                        </div>
                      </div>

                      <!-- Submit Button -->
                      <div class="mt-8 hd:mt-12 flex flex-col gap-y-4 items-center w-full">
                        <button type="submit" class="btn btn--gradient w-[fit-content] flex items-center max-h-[48px] lg:max-h-unset gap-x-4 lg:py-7 mx-auto">
                          <span class="text-xs">Get Your Corporate Offer</span>
                        </button>
                        <span class="max-w-[232px] text-[#B4B4B4] text-center text-xs leading-[150%] lg:max-w-[356px] hd:max-w-[488px]">
                          *We value your privacy. Your information is secure and will only be used for business travel inquiries.
                        </span>
                      </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="w-full px-2 pt-20 pb-2 lg:pt-16 lg:pb-10 hd:pt-24 hd:pb-16">
    <div class="w-full flex flex-col gap-y-10 hd:gap-y-14">
        <div class="max-w-[420px] hd:max-w-[650px] text-center mx-auto flex flex-col gap-y-2 hd:gap-y-4">
            <span class="max-w-[300px] mx-auto lg:max-w-none tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Trusted by Industry Leaders Worldwide</span>
            <h2 class="text-2xl hd:text-4xl font-bold leading-[130%]">Real stories of seamless corporate travel.</h2>
        </div>
        <div class="w-full max-w-[500px] md:max-w-[768px] xl:max-w-[1200px] hd:max-w-[1563px] mx-auto">
            <div class="text-center" id="corp-feedbacks"></div>
                <div class="swiper-container corp-feedbacks-carousel">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-y-4 gap-x-4 justify-between flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-1.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Samantha Green</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">HR Manager, Layers</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/layersLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"The seamless booking process and cost-effective rates provided by TravelBusinessClass made our corporate trips stress-free. Their team handles everything, allowing us to focus on business."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            30%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Increase in Booking Efficiency
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            100%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Business-Class Experience
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-3.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Emily Carter</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Procurement Manager, Sisyphus</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/sisyphusLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Working with a dedicated travel concierge team has helped us significantly reduce corporate travel costs. Their attention to detail and custom solutions are truly impressive."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            30%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Reduction in Flight Costs
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            98%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Employee Satisfaction
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-5.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Jason Miller</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Director of Business Development, Catalog</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/catalogLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Handling complex itineraries is now completely stress-free. Every trip is thoughtfully planned with maximum efficiency."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            40%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Time Saved on Planning
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            85%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Increase in Trip Efficiency
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-7.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Rachel Thompson</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">HR Director, Quxtient</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/quxtientLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Our team travels with confidence knowing every detail is covered. The personalized support has made a real difference in how employees experience business trips."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            50%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Boost in Employee Morale
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            70%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Less Travel-Related Stress
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-9.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">David Brooks</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Chief Financial Officer, Hourglass</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/hourglassLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Transparent pricing and consistent communication make every booking easy to trust. We finally have a travel partner that feels like part of the team."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            25%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Drop in Unexpected Costs
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            90%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Trust in Travel Support
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-2.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Lauren Mitchell</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Office Manager, AlphaWave</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/alphaWaveLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"We often face last-minute changes, and having 24/7 support has been a lifesaver. Everything is handled promptly, and I’m never left waiting."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            60%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Faster Problem Solving
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            80%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Operational Efficiency
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-4.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Mark Davis</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">CEO, EasyTax</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/easyTaxLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Each trip is designed with our goals in mind — no generic solutions. We save time, and our team travels smarter."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            35%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Better Itinerary Planning
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            75%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Improved Business Outcomes
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-6.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Olivia Green</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Logistics Lead, Ikigai Labs</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/lkigaiLabsLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"We needed a flexible solution for high-volume travel, and that’s exactly what we got. Flight changes, cancellations, reroutes — all covered quickly."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            45%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                           Fewer Travel Disruptions
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            85%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Response Speed
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8  bg-white rounded-3xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-8.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Michael Reed</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Head of Sales, Peregrin</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/peregrinLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"With travel handled so seamlessly, my team can focus entirely on client meetings. No distractions, no delays — just results."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            50%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                           Time Saved on Planning
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            90%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Sales Productivity
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-reset-before lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px]" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="500" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks">
                            <div class="p-6 lg:p-8 bg-white rounded-xl h-[420px] lg:!w-[344px] lg:!h-[380px] hd:!w-[480px] hd:!h-[330px] flex flex-col justify-between gap-y-4">
                                <div class="flex items-start hd:items-center gap-x-4 justify-between gap-y-4 flex-col hd:flex-row">
                                    <div class="flex gap-x-2">
                                        <img src="./build/img/static/corpClients/review-person-10.png" alt="Avatar 2" class="w-12 h-12 rounded-full object-cover border-white" />
                                        <div class="flex flex-col gap-y-1">
                                            <span class="text-[#282F36] text-base font-bold leading-[130%]">Samantha Lewis</span>
                                            <span class="text-[#838D95] text-sm leading-[150%] max-w-[240px]">Events Manager, Spherule</span>
                                        </div>
                                    </div>
                                    <img class="w-[105px] mb-auto" src="./build/img/static/corpClients/spheruleLogo.svg" alt="">
                                </div>
                                <div>
                                    <span class="text-base text-[#282F36] leading-[150%]">"Coordinating group travel used to be a headache. Now it's effortless. Every guest arrives on time, comfortably and stress-free."</span>
                                </div>
                                <div class="w-full grid grid-cols-2 gap-x-4">
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            40%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                          Time Saved on Coordination
                                        </span>
                                    </div>
                                    <div class="flex flex-col gap-y-1">
                                        <span class="text-lg text-[#282F36] font-bold leading-[130%]">
                                            80%
                                        </span>
                                        <span class="text-xs text-[#838D95] leading-[150%]">
                                            Event Success Rate
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="corp-feedbacks-carousel-pagination swiper-pagination" data-aos="fade-in" data-aos-duration="1200" data-aos-delay="900" data-aos-offset="300" data-aos-easing="ease-out-expo" data-aos-anchor="#corp-feedbacks"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="max-w-[800px] lg:max-w-[1475px] mx-auto px-2 pt-16 pb-2 lg:py-10 hd:pt-24 hd:pb-16 hd:px-0">
    <div class="w-full bg-white rounded-3xl px-2 lg:px-8 pt-14 lg:pt-16 pb-8 lg:pb-10 hd:p-14 hd:pt-20 flex flex-col gap-y-8 lg:gap-y-10 hd:gap-y-14">
        <div class="max-w-[970px] text-center mx-auto flex flex-col gap-y-4">
            <div class="max-w-[400px] hd:max-w-[550px] px-2 sm:px-0 mx-auto flex flex-col items-center gap-y-2 hd:gap-y-4">
                <span class="max-w-[200px] lg:max-w-none tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Save More, Travel Better with TravelCash</span>
                <h2 class="text-2xl hd:text-4xl font-bold leading-[130%]">Earn more every day with Exclusive Rewards!</h2>
            </div>
            <span class="max-w-[600px] hd:max-w-[970px] text-sm hd:text-base text-[#71777D] leading-[150%]">With TravelCash, business travel is not just easier—it’s smarter. Unlock exclusive rewards, reduce costs, and enjoy seamless corporate trips with every booking.</span>
        </div>
        <div class="flex flex-col gap-y-2 lg:grid lg:grid-cols-3 gap-x-6">
            <div class="flex flex-col md:grid md:grid-cols-2 lg:flex gap-x-2 gap-y-2">
                <div class="p-6 rounded-3xl bg-[#F7F7F7] mx-auto md:mx-0 max-w-[350px] md:max-w-none">
                    <div class="flex flex-col min-h-[190px] lg:h-[200px]">
                        <div class="p-3 rounded-lg bg-white shadow w-[fit-content]">
                            <img class="w-4 h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/user-plus.svg" alt="">
                        </div>
                        <div class="flex flex-col gap-y-3 mt-auto">
                            <span class="text-base hd:text-lg font-bold leading-[130%]">Enjoy Exclusive Membership Benefits</span>
                            <p class="text-sm hd:text-base font-base leading-[130%] text-[#71777D]">Sign up today and receive a <span class="text-gradient font-bold">$100 Welcome Bonus</span> — yours to use towards future bookings!</p>
                        </div>
                    </div>
                </div>
                <div class="p-6 rounded-3xl bg-[#F7F7F7] mx-auto md:mx-0 max-w-[350px] md:max-w-none">
                    <div class="flex flex-col min-h-[190px] lg:h-[200px]">
                        <div class="p-3 rounded-lg bg-white shadow w-[fit-content]">
                            <img class="w-4 h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/gift.svg" alt="">
                        </div>
                        <div class="flex flex-col gap-y-3 mt-auto">
                            <span class="text-base hd:text-lg font-bold leading-[130%]">Get 1% Back on Every Ticket Purchase</span>
                            <p class="text-sm hd:text-base font-base leading-[130%] text-[#71777D]">For each eligible booking, earn <span class="font-bold text-gradient">1% back</span> in TravelCash, or <span class="font-bold text-gradient">5%</span> for every insured flight with Ticket Protection.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="min-h-[400px] rounded-4xl h-full w-full bg-cover bg-center bg-no-repeat" style="background-image: url(./build/img/static/corpClients/earn-more.jpg);"></div>
            <div class="flex flex-col md:grid md:grid-cols-2 lg:flex gap-x-2 gap-y-2">
                <div class="p-6 rounded-3xl bg-[#F7F7F7] mx-auto md:mx-0 max-w-[350px] md:max-w-none">
                    <div class="flex flex-col min-h-[190px] lg:h-[200px]">
                        <div class="p-3 rounded-lg bg-white shadow w-[fit-content]">
                            <img class="w-4 h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/plane.svg" alt="">
                        </div>
                        <div class="flex flex-col gap-y-3 mt-auto">
                            <span class="text-base hd:text-lg font-bold leading-[130%]">TravelCash with Every Ticket Purchase</span>
                            <p class="text-sm hd:text-base font-base leading-[130%] text-[#71777D]">We value your loyalty! Every time you book a flight, you’ll earn <span class="text-gradient font-bold">TravelCash rewards</span> to use for future trips. </p>
                        </div>
                    </div>
                </div>
                <div class="p-6 rounded-3xl bg-[#F7F7F7] mx-auto md:mx-0 max-w-[350px] md:max-w-none">
                    <div class="flex flex-col min-h-[190px] lg:h-[200px]">
                        <div class="p-3 rounded-2xl bg-white shadow w-[fit-content]">
                            <img class="w-4 h-4 hd:w-5 hd:h-5" src="./build/img/static/corpClients/users.svg" alt="">
                        </div>
                        <div class="flex flex-col gap-y-3 mt-auto">
                            <span class="text-base hd:text-lg font-bold leading-[130%]">Get $100 TravelCash for Every Referral</span>
                            <p class="text-sm hd:text-base font-base leading-[130%] text-[#71777D]">Invite your friends to book their first flight and <span class="text-gradient font-bold">earn $100</span> in TravelCash — yours to use on future trips!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="max-w-[350px] w-full mx-auto lg:mx-0 lg:max-w-none lg:grid lg:grid-cols-3 gap-x-6">
            <div></div>
            <button class="get-corporate-offer btn btn--gradient w-full flex items-center gap-x-4 pr-4 max-h-[48px] lg:max-h-unset py-6 hd:py-8">
                <span class="text-xs">Start earning</span>
                <svg class="w-4 h-4 fill-current text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M6.282 2.304a1 1 0 011.414-.022l9.273 9a1 1 0 010 1.436l-9.273 9a1 1 0 01-1.392-1.436L14.837 12 6.304 3.718a1 1 0 01-.022-1.414z" clip-rule="evenodd"/>
                </svg>
            </button>
            <div></div>
        </div>
    </div>
</section>
<section class="max-w-[1475px] mx-auto w-full px-2 lg:p-10 pt-20 lg:pt-16 hd:pt-24 hd:pb-16 hd:px-0">
    <div class="w-full flex flex-col gap-y-10 hd:gap-y-14">
        <div class="max-w-[650px] text-center mx-auto flex flex-col gap-y-2 hd:gap-y-4">
            <span class="max-w-[170px] lg:max-w-none tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Your Questions, Answered</span>
            <h2 class="max-w-[300px] lg:max-w-none text-2xl hd:text-4xl font-bold leading-[130%]">Frequently Asked Questions.</h2>
        </div>
        <div class="w-full">
            <div class="flex flex-wrap justify-center gap-y-6 lg:grid lg:grid-cols-3">
                <div class="flex flex-col items-center gap-y-4 lg:px-4 w-[280px] lg:mx-auto hd:w-[440px]">
                    <div class="p-3.5 lg:p-3 hd:p-4 bg-white rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-[24px] hd:h-[24px]" src="./build/img/static/corpClients/plane.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2 text-center mt-auto">
                        <span class="text-[#282F36] text-base lg:text-sm hd:text-lg font-bold leading-[130%]">Can I book flights on credit?</span>
                        <span class="text-[#71777D] text-base lg:text-sm hd:text-base">Yes, we offer <span class="font-bold">flexible credit options</span> for corporate clients. Contact us to learn more about eligibility and terms.</span>
                    </div>
                </div>
                <div class="flex flex-col items-center gap-y-4 lg:px-4 w-[280px] lg:mx-auto hd:w-[440px]">
                    <div class="p-3.5 lg:p-3 hd:p-4 bg-white rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-[24px] hd:h-[24px]" src="./build/img/static/corpClients/badge-percent.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2 text-center mt-auto">
                        <span class="text-[#282F36] text-base lg:text-sm hd:text-lg font-bold leading-[130%]">Are there discounts for group flights?</span>
                        <span class="text-[#71777D] text-base lg:text-sm hd:text-base">Absolutely! We provide <span class="font-bold">special rates for group bookings.</span> Reach out to our team for a tailored offer.</span>
                    </div>
                </div>
                <div class="flex flex-col items-center gap-y-4 lg:px-4 w-[280px] lg:mx-auto hd:w-[440px]">
                    <div class="p-3.5 lg:p-3 hd:p-4 bg-white rounded-full w-[fit-content]">
                        <img class="w-5 h-5 lg:w-4 lg:h-4 hd:w-[24px] hd:h-[24px]" src="./build/img/static/corpClients/headset.svg" alt="">
                    </div>
                    <div class="flex flex-col gap-y-2 text-center mt-auto">
                        <span class="text-[#282F36] text-base lg:text-sm hd:text-lg font-bold leading-[130%]">How quickly do you respond?</span>
                        <span class="text-[#71777D] text-base lg:text-sm hd:text-base">Our team responds within <span class="font-bold">15 minutes</span> during business hours, ensuring you get the support you need promptly.</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-3xl px-4 lg:px-10 hd:px-0 pt-14 pb-8 flex flex-col gap-y-6 lg:gap-y-8">
            <div class="flex flex-col gap-y-6 text-center">
                <div class="flex flex-col gap-y-2">
                    <span class="max-w-[230px] mx-auto lg:max-w-none tracking-[1.2px] text-gradient text-xs hd:text-sm uppercase font-bold">Couldn’t find what you were looking for?</span>
                    <span class="font-bold text-2xl">Your travel advisor is one phone call away!</span>
                </div>
                <div class="flex justify-center -space-x-4">
                  <img src="./build/img/static/corpClients/female-avatar-6.png" alt="Avatar 2" class="w-11 h-11 lg:w-14 lg:h-14 rounded-full object-cover border-white" />
                  <img src="./build/img/static/corpClients/female-avatar-5.png" alt="Avatar 1" class="w-11 h-11 lg:w-14 lg:h-14 rounded-full object-cover border-white" />
                  <img src="./build/img/static/corpClients/female-avatar-4.png" alt="Avatar 5" class="w-11 h-11 lg:w-14 lg:h-14 rounded-full object-cover border-white" />
                  <img src="./build/img/static/corpClients/female-avatar-3.png" alt="Avatar 3" class="w-11 h-11 lg:w-14 lg:h-14 rounded-full object-cover border-white" />
                  <img src="./build/img/static/corpClients/male-avatar-2.png" alt="Avatar 6" class="w-11 h-11 lg:w-14 lg:h-14 rounded-full object-cover border-white" />
                </div>
            </div>
            <div class="flex flex-col gap-y-6 text-center max-w-[1000px] mx-auto">
                <p class="text-lg text-[#282F36] leading-[130%]"><span class="font-bold">Over 130 highly skilled travel advisors</span>  with over 10 years experience in the industry.</p>
                <span class="leading-[150%] text-lg">With travelbusinessclass, you get your own dedicated travel advisor, that will take care of all your travel needs and grant you access to the best value business and first class offers tailored to your smallest request.</span>
            </div>
            <button class="get-corporate-offer btn btn--gradient w-full max-w-[350px] lg:w-[fit-content] mx-auto flex items-center gap-x-4 max-h-[48px] lg:max-h-unset lg:py-7" >
                <span class="text-xs">Contact us</span>
            </button>
        </div>
    </div>
</section>
<?= SubscribeWidget::widget([])?>
