<?php



/* @var $this \yii\web\View */
/* @var $lead \common\models\Lead */

use yii\helpers\Html;

$this->title = 'Success';

\backend\assets\BuildInitAsset::register($this);

?>

<div class="offer-success">
    <div class="offer-success__hero">
        <div class="offer-success__hero__container">
            <div class="offer-success__hero__content">
                <span class="display-gradient uppercase">Thank you FOR CHOOSING</span>
                <h1 class="mt-2 text-2xl md:text-4xl text-white">Travel Business Class!</h1>
                <p class="mt-8 font-bold text-base md:text-lg lg:max-w-lg xl:max-w-lg text-white">For tailored Travel Assistance, we have appointed an agent who will be contacting you soon!</p>
                <div class="flex gap-6 flex-col text-center lg:flex-row justify-between mt-6 lg:max-w-lg xl:max-w-lg mt-4 md:mt-24 lg:mt-24 xl:mt-24" style="gap: 24px;">
                    <div>
                            <a href="tel:+***********" class="btn btn--gradient items-center p-0 py-7">
                                <div class="rounded-11xl p-4 bg-white">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20.3124 11.82C20.6932 9.58505 19.7562 7.30974 18.2709 5.74073C16.7897 4.17597 14.6318 3.18549 12.4844 3.76872L12.835 5.04934C14.3533 4.63699 16.0317 5.31027 17.3012 6.65129C18.5665 7.98804 19.2945 9.86051 18.9985 11.598L20.3124 11.82Z" fill="url(#paint0_linear_6503_6460)"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.66406 7.92197C2.66406 5.79859 4.39224 4.07715 6.52419 4.07715C6.94052 4.07715 7.34273 4.14304 7.7201 4.26542C8.20271 4.42192 8.48615 4.83917 8.55891 5.25508L9.21486 9.00206L9.32871 9.80031C9.38755 10.2109 9.25376 10.6851 8.85812 10.9846C8.44793 11.2951 7.97279 11.5262 7.45615 11.6538C7.92896 12.7438 8.60478 13.7352 9.44829 14.5753C10.2922 15.416 11.2882 16.0894 12.3831 16.5603C12.5112 16.046 12.7429 15.5729 13.0544 15.1645C13.3551 14.7702 13.8314 14.6368 14.2439 14.6955L15.0443 14.8093L18.8075 15.4622C19.2252 15.5347 19.6443 15.8172 19.8014 16.298C19.924 16.6736 19.9901 17.0739 19.9901 17.4883C19.9901 19.6117 18.2619 21.3331 16.1299 21.3331C15.9587 21.3331 15.7898 21.322 15.6239 21.3003C14.8507 21.2714 14.0826 21.176 13.3281 21.016C12.5283 20.8463 11.7437 20.6038 10.9846 20.2906C9.35381 19.6178 7.87204 18.6316 6.6239 17.3884C5.37576 16.1452 4.38568 14.6693 3.7102 13.0449C3.39575 12.2888 3.15234 11.5074 2.98197 10.7107C2.82109 9.95841 2.72534 9.19253 2.69642 8.42147C2.67505 8.25769 2.66406 8.09095 2.66406 7.92197ZM14.8257 16.1188L14.0816 16.0132C13.8876 16.28 13.7451 16.5856 13.6681 16.9156C13.589 17.2547 13.3791 17.5512 13.0936 17.735C12.7999 17.9241 12.4002 18.0053 12.0031 17.8415C10.6952 17.3019 9.50683 16.511 8.50583 15.5139C7.50482 14.5169 6.71079 13.3332 6.16905 12.0305C6.0048 11.6355 6.08624 11.2379 6.27589 10.9458C6.46022 10.6618 6.75751 10.453 7.09768 10.3745C7.42983 10.2978 7.73726 10.1557 8.00559 9.96189L7.89563 9.19429L7.30529 5.82223L7.25063 5.50989C7.0213 5.44149 6.77763 5.40453 6.52419 5.40453C5.35053 5.40453 4.36366 6.20142 4.0793 7.2817C4.02549 7.48613 3.99683 7.70071 3.99683 7.92197C3.99683 8.04162 4.00517 8.15896 4.0212 8.27353L4.02597 8.30761L4.02717 8.34201C4.05186 9.04672 4.13849 9.74677 4.28551 10.4342C4.43899 11.1519 4.65826 11.8558 4.94153 12.537C5.55006 14.0003 6.44197 15.3299 7.56636 16.4499C8.69074 17.5698 10.0256 18.4582 11.4947 19.0643C12.1785 19.3464 12.8853 19.5649 13.6058 19.7177C14.2953 19.864 14.9975 19.9503 15.7043 19.9749L15.7392 19.9762L15.7738 19.981C15.8898 19.9973 16.0087 20.0058 16.1299 20.0058C17.5257 20.0058 18.6573 18.8787 18.6573 17.4883C18.6573 17.2361 18.6202 16.9935 18.5517 16.7652L14.8257 16.1188Z" fill="url(#paint1_linear_6503_6460)"/>
                                        <path d="M16.3559 8.02217C17.2132 8.92528 17.6613 10.2596 17.3003 11.5549L16.0161 11.1999C16.2343 10.4167 15.9723 9.55029 15.3874 8.93407C14.8119 8.32776 13.9715 8.0134 13.0662 8.26272L12.7109 6.98335C14.1553 6.58559 15.4892 7.10915 16.3559 8.02217Z" fill="url(#paint2_linear_6503_6460)"/>
                                        <defs>
                                        <linearGradient id="paint0_linear_6503_6460" x1="20.3974" y1="3.59961" x2="10.7695" y2="8.32821" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#590C32"/>
                                        <stop offset="1" stop-color="#9D1D5A"/>
                                        </linearGradient>
                                        <linearGradient id="paint1_linear_6503_6460" x1="19.9901" y1="4.07715" x2="-0.736543" y2="14.6949" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#590C32"/>
                                        <stop offset="1" stop-color="#9D1D5A"/>
                                        </linearGradient>
                                        <linearGradient id="paint2_linear_6503_6460" x1="17.4259" y1="6.84863" x2="11.7804" y2="9.73436" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#590C32"/>
                                        <stop offset="1" stop-color="#9D1D5A"/>
                                        </linearGradient>
                                        </defs>
                                        </svg>
                                </div>
                                <p class="font-bold text-lg mr-8 ml-4">
                                    +1 (855) 855-1221
                                </p>
                            </a>
                            <p class="text-xs font-normal text-grey-500 text-center">
                                For immediate questions and special requests
                            </p>
                    </div>
                    <div>
                        <div tabindex="0" id="jivo_callback_btn" class="btn btn--gradient items-center p-0 py-7 cursor-pointer">
                            <div class="rounded-11xl p-4 bg-white">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20.75 15.3879C20.7499 14.7315 20.4891 14.1021 20.025 13.6381C19.5609 13.174 18.9315 12.9132 18.2752 12.9131H6.72523C6.4002 12.913 6.07834 12.977 5.77804 13.1013C5.47773 13.2257 5.20486 13.408 4.97501 13.6378C4.74515 13.8676 4.56282 14.1404 4.43842 14.4407C4.31403 14.741 4.25 15.0628 4.25 15.3879V16.2131C4.25 19.4651 7.31923 22.8131 12.5 22.8131C17.6808 22.8131 20.75 19.4651 20.75 16.2131V15.3879ZM5.9 15.3879C5.90012 15.1691 5.98712 14.9593 6.14187 14.8046C6.29662 14.65 6.50645 14.5631 6.72523 14.5631H18.2752C18.4939 14.5632 18.7036 14.6501 18.8583 14.8048C19.0129 14.9594 19.0999 15.1692 19.1 15.3879V16.2131C19.1 18.5854 16.7369 21.1631 12.5 21.1631C8.26308 21.1631 5.9 18.5854 5.9 16.2131V15.3879Z" fill="url(#paint0_linear_6503_6484)"/>
                                    <path d="M17.0403 6.72516C17.0401 5.74714 16.724 4.79535 16.1389 4.01162C15.5539 3.22789 14.7313 2.65417 13.7937 2.37595C12.8561 2.09773 11.8537 2.1299 10.9358 2.46768C10.018 2.80545 9.23386 3.43075 8.70028 4.25039H5.90336C5.79501 4.25033 5.68771 4.27161 5.58759 4.31304C5.48747 4.35446 5.39649 4.4152 5.31986 4.49179C5.24322 4.56839 5.18243 4.65933 5.14095 4.75942C5.09947 4.85952 5.07812 4.96681 5.07813 5.07516V8.37516C5.07813 8.40146 5.07812 8.42639 5.08136 8.45131H5.07813V10.0252C5.07813 10.3502 5.14215 10.6721 5.26654 10.9724C5.39093 11.2727 5.57326 11.5456 5.8031 11.7754C6.03295 12.0053 6.30582 12.1876 6.60613 12.312C6.90643 12.4364 7.2283 12.5004 7.55336 12.5004H8.19166C8.36148 12.5001 8.52847 12.4569 8.67722 12.375C8.82597 12.2931 8.95169 12.1751 9.04277 12.0317C9.13385 11.8884 9.18735 11.7245 9.19833 11.555C9.20932 11.3856 9.17743 11.2161 9.10562 11.0622C9.03381 10.9083 8.92439 10.775 8.78746 10.6746C8.65053 10.5741 8.4905 10.5098 8.32215 10.4876C8.15381 10.4653 7.98256 10.4858 7.82424 10.5472C7.66592 10.6086 7.52561 10.7089 7.41628 10.8388C7.22399 10.8063 7.04942 10.7068 6.92354 10.5578C6.79765 10.4089 6.72859 10.2202 6.72859 10.0252V9.20039H7.14074C7.61273 9.20072 8.07489 9.06552 8.47228 8.81085C8.93923 9.71456 9.6963 10.4352 10.6219 10.8569C11.5476 11.2787 12.5881 11.3772 13.5765 11.1367C14.5648 10.8961 15.4437 10.3304 16.0719 9.53033C16.7002 8.7303 17.0409 7.74237 17.0403 6.72516ZM9.61551 6.72516C9.61551 5.95931 9.91974 5.22484 10.4613 4.6833C11.0028 4.14177 11.7373 3.83754 12.5031 3.83754C13.269 3.83754 14.0034 4.14177 14.545 4.6833C15.0865 5.22484 15.3907 5.95931 15.3907 6.72516C15.3907 7.491 15.0865 8.22548 14.545 8.76701C14.0034 9.30854 13.269 9.61277 12.5031 9.61277C11.7373 9.61277 11.0028 9.30854 10.4613 8.76701C9.91974 8.22548 9.61551 7.491 9.61551 6.72516ZM7.96551 6.72516C7.96551 6.94394 7.87863 7.15377 7.72397 7.30852C7.56931 7.46327 7.35953 7.55026 7.14074 7.55039H6.72813V5.90039H7.96551V6.72516Z" fill="url(#paint1_linear_6503_6484)"/>
                                    <defs>
                                    <linearGradient id="paint0_linear_6503_6484" x1="20.75" y1="12.9131" x2="6.28846" y2="25.2103" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#590C32"/>
                                    <stop offset="1" stop-color="#9D1D5A"/>
                                    </linearGradient>
                                    <linearGradient id="paint1_linear_6503_6484" x1="17.0407" y1="2.18848" x2="3.66167" y2="10.1072" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#590C32"/>
                                    <stop offset="1" stop-color="#9D1D5A"/>
                                    </linearGradient>
                                    </defs>
                                    </svg>
                            </div>
                            <p class="font-bold text-lg mr-8 ml-4 normal-case">
                                Call Back
                            </p>
                        </div>
                        <p class="text-xs font-normal text-center text-grey-500">
                            If you'd like us to reach out
                        </p>
                    </div>
                </div>
            </div>
            <div class="offer-success__hero__attendant">
                <img loading=lazy src="<?=Yii::getAlias('@cdn/build/img/static/wlc-img-blurred.png')?>" class="offer-success__hero__attendant__plane"
                     alt="">
                     <img loading=lazy src="<?=Yii::getAlias('@cdn/build/img/static/flight-attendant-cropped.png')?>" class="offer-success__hero__attendant__steward"
                     alt="">
            </div>
        </div>

    </div>

        <div class="offer-success__benefits">
            <div class="flex">
                <div class="flex-shrink-0 bg-white rounded-full flex items-center justify-center w-10 h-10">
                    <svg class="w-4 h-4 fill-gradient" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <path d="M45.183 4.875H29.645c-1.066 0-2.56.619-3.314 1.372L7.067 25.511a1.947 1.947 0 000 2.748l16.674 16.674a1.947 1.947 0 002.748 0l19.263-19.264c.757-.756 1.373-2.248 1.373-3.314V6.817a1.95 1.95 0 00-1.942-1.942zm-9.711 15.537a3.885 3.885 0 11.002-7.77 3.885 3.885 0 01-.002 7.77z" />
                    </svg>
                </div>
                <div class="mx-2 mt-2 text-sm">
                    <b class="text-base">Exclusive Deals</b>
                    <p class="mt-2">Privately negotiated airfares from over 50 airlines with discounts of <b>up to 60%</b></p>
                </div>
            </div>
            <div class="flex">
                <div class="flex-shrink-0 bg-white rounded-full flex items-center justify-center w-10 h-10">
                        <svg class="stroke-gradient" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.7951 21.0002H4.99985C4.46941 21 3.96074 20.7892 3.58571 20.4141C3.21068 20.039 3 19.5303 3 18.9998V6.99985C3 6.46945 3.2107 5.96079 3.58574 5.58574C3.96079 5.2107 4.46945 5 4.99985 5H16.9998C17.5303 5 18.039 5.21068 18.4141 5.58571C18.7892 5.96074 19 6.46941 19.0002 6.99985V11" stroke="url(#paint0_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M17.9962 22.0001C18.5251 22.0058 19.0499 21.9065 19.5402 21.708C20.0305 21.5096 20.4765 21.2158 20.8525 20.8438C21.2285 20.4718 21.527 20.029 21.7308 19.5409C21.9345 19.0527 22.0394 18.5291 22.0394 18.0002C22.0394 17.4712 21.9345 16.9476 21.7308 16.4594C21.527 15.9713 21.2285 15.5285 20.8525 15.1565C20.4765 14.7845 20.0305 14.4907 19.5402 14.2923C19.0499 14.0938 18.5251 13.9945 17.9962 14.0002C16.9428 14.0115 15.9364 14.4379 15.1955 15.1868C14.4546 15.9358 14.0391 16.9467 14.0391 18.0002C14.0391 19.0536 14.4546 20.0646 15.1955 20.8135C15.9364 21.5624 16.9428 21.9888 17.9962 22.0001Z" stroke="url(#paint1_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15 3V7.00015" stroke="url(#paint2_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7 3V7.00015" stroke="url(#paint3_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3 11H19.0002" stroke="url(#paint4_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M18 16.4961V18.0002L19.0002 18.9999" stroke="url(#paint5_linear_6503_7093)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <defs>
                            <linearGradient id="paint0_linear_6503_7093" x1="19.0002" y1="5" x2="-0.172536" y2="14.782" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            <linearGradient id="paint1_linear_6503_7093" x1="22.0394" y1="14" x2="12.4527" y2="18.8911" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            <linearGradient id="paint2_linear_6503_7093" x1="16" y1="3" x2="14.514" y2="3.18954" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            <linearGradient id="paint3_linear_6503_7093" x1="8" y1="3" x2="6.51397" y2="3.18954" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            <linearGradient id="paint4_linear_6503_7093" x1="19.0002" y1="11" x2="18.6429" y2="13.9162" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            <linearGradient id="paint5_linear_6503_7093" x1="19.0002" y1="16.4961" x2="17.55" y2="16.7916" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#590C32"/>
                            <stop offset="1" stop-color="#9D1D5A"/>
                            </linearGradient>
                            </defs>

                    </svg>

                </div>
                <div class="mx-2 mt-2 text-sm">
                    <b class="text-base">Last minute bookings</b>
                    <p class="mt-2">Book your trip stress free even a few hours before boarding. </p>
                </div>
            </div>
            <div class="flex">
                <div class="flex-shrink-0 bg-white rounded-full flex items-center justify-center w-10 h-10">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.0485 11.6036C15.0485 10.8882 14.4665 10.3066 13.7515 10.3066C13.4054 10.3066 13.08 10.4414 12.8349 10.6865L9.83031 13.6916C9.42162 14.098 8.93748 14.4206 8.40508 14.6414C8.75934 14.1737 8.95121 13.6031 8.95154 13.0163C8.95056 12.2981 8.66481 11.6096 8.15694 11.1017C7.64908 10.5938 6.96054 10.3081 6.24231 10.3071C5.52391 10.3078 4.83514 10.5935 4.32708 11.1014C3.81901 11.6093 3.53313 12.2979 3.53215 13.0163C3.53215 13.8623 3.92262 14.6183 4.53185 15.1163C2.80846 15.4422 1.5 16.9574 1.5 18.7745V22.5H9.29031V18.1551C9.29031 17.9894 9.35492 17.8339 9.47169 17.7166L14.6686 12.5197C14.7893 12.3997 14.8851 12.2569 14.9503 12.0997C15.0155 11.9424 15.0488 11.7738 15.0485 11.6036ZM6.58062 15.726L6.88523 15.6836C6.798 16.0943 6.54046 16.4036 6.24185 16.4036C5.95385 16.4036 5.70323 16.1156 5.60769 15.726H6.58062ZM4.20969 13.0163C4.2103 12.4776 4.4246 11.961 4.80557 11.5801C5.18654 11.1991 5.70307 10.9848 6.24185 10.9842C6.78062 10.9848 7.29715 11.1991 7.67812 11.5801C8.05909 11.961 8.27339 12.4776 8.274 13.0163C8.27339 13.5551 8.05909 14.0716 7.67812 14.4526C7.29715 14.8336 6.78062 15.0479 6.24185 15.0485C5.70307 15.0479 5.18654 14.8336 4.80557 14.4526C4.4246 14.0716 4.2103 13.5551 4.20969 13.0163ZM14.1895 12.0416L8.99262 17.2385C8.87183 17.3586 8.77606 17.5014 8.71086 17.6587C8.64566 17.8161 8.61232 17.9848 8.61277 18.1551V21.8229H3.87092V17.7582H3.19385V21.8229H2.17754V18.7745C2.17754 17.1974 3.38123 15.8963 4.91815 15.7417C5.04877 16.5056 5.592 17.0806 6.24185 17.0806C6.93646 17.0806 7.50323 16.4225 7.58123 15.5806C8.61355 15.4012 9.56578 14.9088 10.3089 14.1702L13.3135 11.1656C13.4311 11.0564 13.5864 10.997 13.7468 11C13.9072 11.0029 14.0602 11.068 14.1737 11.1814C14.2871 11.2949 14.3522 11.4479 14.3551 11.6083C14.3581 11.7687 14.2987 11.924 14.1895 12.0416Z" fill="url(#paint0_linear_6503_7105)"/>
                        <path d="M20.4706 15.1237C20.7859 14.8703 21.0405 14.5494 21.2154 14.1846C21.3903 13.8198 21.4812 13.4204 21.4813 13.0159C21.4803 12.2976 21.1945 11.609 20.6866 11.1011C20.1786 10.5932 19.4899 10.3075 18.7716 10.3066C18.0533 10.3075 17.3647 10.5932 16.8567 11.1011C16.3487 11.609 16.0629 12.2976 16.0619 13.0159C16.0619 14.3059 16.9689 15.3863 18.1776 15.6577C17.7549 16.0024 17.4497 16.47 17.3044 16.9957L16.8193 18.774H13.5216C13.2073 18.7743 12.9059 18.8992 12.6837 19.1214C12.4614 19.3436 12.3363 19.6449 12.3359 19.9593C12.3362 20.2736 12.4612 20.5751 12.6835 20.7974C12.9058 21.0197 13.2072 21.1447 13.5216 21.1449H16.7395V22.4996H22.4972V17.6913C22.4972 16.4465 21.6313 15.402 20.4706 15.1237ZM16.7395 13.0159C16.7401 12.4771 16.9544 11.9606 17.3354 11.5796C17.7163 11.1986 18.2329 10.9843 18.7716 10.9837C19.3104 10.9843 19.8269 11.1986 20.2079 11.5796C20.5889 11.9606 20.8032 12.4771 20.8038 13.0159C20.8032 13.5546 20.5889 14.0712 20.2079 14.4521C19.8269 14.8331 19.3104 15.0474 18.7716 15.048C18.2329 15.0474 17.7163 14.8331 17.3354 14.4521C16.9544 14.0712 16.7401 13.5546 16.7395 13.0159ZM21.8201 21.822H17.417V21.1449H17.7004C17.9848 21.1454 18.2622 21.0561 18.493 20.8897C18.7237 20.7234 18.8962 20.4886 18.9858 20.2186L20.1087 16.8489L19.4667 16.6343L18.3433 20.004C18.2984 20.139 18.2121 20.2564 18.0967 20.3396C17.9813 20.4228 17.8427 20.4675 17.7004 20.4674H13.5216C13.2415 20.4674 13.0139 20.2394 13.0139 19.9597C13.0139 19.6791 13.2415 19.4516 13.5216 19.4516H17.3367L17.9579 17.1739C18.072 16.7583 18.3192 16.3915 18.6617 16.1299C19.0041 15.8683 19.423 15.7263 19.8539 15.7256C20.3752 15.7262 20.875 15.9335 21.2435 16.3021C21.6121 16.6707 21.8195 17.1704 21.8201 17.6917V21.822Z" fill="url(#paint1_linear_6503_7105)"/>
                        <path d="M5.5625 4.8877H6.23958V5.56523H5.5625V4.8877Z" fill="url(#paint2_linear_6503_7105)"/>
                        <path d="M6.92188 4.8877H10.9871V5.56523H6.92234V4.8877H6.92188Z" fill="url(#paint3_linear_6503_7105)"/>
                        <path d="M5.5625 6.24219H10.9819V6.91973H5.5625V6.24219Z" fill="url(#paint4_linear_6503_7105)"/>
                        <path d="M5.5625 7.59668H10.9819V8.27422H5.5625V7.59668Z" fill="url(#paint5_linear_6503_7105)"/>
                        <path d="M20.4688 4.20945H21.1458V4.88698H20.4688V4.20898V4.20945Z" fill="url(#paint6_linear_6503_7105)"/>
                        <path d="M13.0161 4.20945H19.791V4.88698H13.0156V4.20898L13.0161 4.20945Z" fill="url(#paint7_linear_6503_7105)"/>
                        <path d="M13.0156 5.56445H21.1447V6.24153H13.0156V5.56445Z" fill="url(#paint8_linear_6503_7105)"/>
                        <path d="M13.0156 6.91895H21.1447V7.59648H13.0156V6.91895Z" fill="url(#paint9_linear_6503_7105)"/>
                        <path d="M20.8069 1.5H13.3563C12.9072 1.50037 12.4766 1.67894 12.159 1.99652C11.8414 2.3141 11.6628 2.74472 11.6625 3.19385V3.53215H5.56555C5.20632 3.53252 4.86191 3.67542 4.60794 3.92948C4.35396 4.18354 4.21118 4.528 4.21094 4.88723V8.274C4.21118 8.63331 4.35403 8.97784 4.6081 9.23191C4.86217 9.48599 5.2067 9.62883 5.56601 9.62908H8.61402V11.2782L11.0879 9.62908H11.6625C11.9145 9.62747 12.1611 9.55588 12.3747 9.42231C12.5884 9.28874 12.7608 9.09843 12.8726 8.87262C13.0268 8.91877 13.1869 8.95154 13.3563 8.95154H14.2697L16.7436 10.6006V8.95154H20.8079C21.257 8.95117 21.6876 8.7726 22.0052 8.45502C22.3228 8.13744 22.5013 7.70682 22.5017 7.25769V3.19385C22.5012 2.7448 22.3226 2.3143 22.005 1.99682C21.6875 1.67934 21.2569 1.50083 20.8079 1.50046L20.8069 1.5ZM11.6625 8.95154H10.8825L9.29155 10.0122V8.95154H5.56601C5.38639 8.95129 5.2142 8.87983 5.08719 8.75282C4.96018 8.62581 4.88872 8.45362 4.88848 8.274V4.88769C4.88848 4.51385 5.19217 4.21015 5.56601 4.21015H11.6629V7.25862C11.6629 7.78015 11.9048 8.24169 12.2768 8.55277C12.2233 8.67099 12.137 8.77141 12.0282 8.84216C11.9195 8.91291 11.7927 8.95103 11.6629 8.952L11.6625 8.95154ZM21.8237 7.25769C21.8235 7.52708 21.7164 7.78537 21.5259 7.9759C21.3355 8.16643 21.0772 8.27363 20.8079 8.274H16.0656V9.33462L14.4746 8.274H13.3559C13.0865 8.27376 12.8282 8.16667 12.6377 7.97622C12.4471 7.78578 12.3399 7.52754 12.3396 7.25815V3.19338C12.3396 2.63354 12.7956 2.17754 13.3559 2.17754H20.8069C21.3672 2.17754 21.8228 2.63354 21.8228 3.19338V7.25815L21.8237 7.25769Z" fill="url(#paint10_linear_6503_7105)"/>
                        <path d="M13.0161 2.85547H13.6936V3.53255H13.0156L13.0161 2.85547Z" fill="url(#paint11_linear_6503_7105)"/>
                        <path d="M14.3672 2.85547H15.0447V3.53255H14.3672V2.85547Z" fill="url(#paint12_linear_6503_7105)"/>
                        <path d="M15.7266 2.85547H21.1459V3.53255H15.7266V2.85547Z" fill="url(#paint13_linear_6503_7105)"/>
                        <defs>
                        <linearGradient id="paint0_linear_6503_7105" x1="15.0485" y1="10.3066" x2="-0.436059" y2="19.0849" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_6503_7105" x1="22.4972" y1="10.3066" x2="9.50114" y2="15.8324" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint2_linear_6503_7105" x1="6.23958" y1="4.8877" x2="5.42802" y2="5.30147" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint3_linear_6503_7105" x1="10.9871" y1="4.8877" x2="10.3951" y2="6.69983" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint4_linear_6503_7105" x1="10.9819" y1="6.24219" x2="10.5183" y2="8.1341" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint5_linear_6503_7105" x1="10.9819" y1="7.59668" x2="10.5183" y2="9.48859" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint6_linear_6503_7105" x1="21.1458" y1="4.20898" x2="20.334" y2="4.6226" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint7_linear_6503_7105" x1="19.791" y1="4.20898" x2="19.412" y2="6.14152" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint8_linear_6503_7105" x1="21.1447" y1="5.56445" x2="20.826" y2="7.51658" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint9_linear_6503_7105" x1="21.1447" y1="6.91895" x2="20.8256" y2="8.87233" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint10_linear_6503_7105" x1="22.5017" y1="1.5" x2="8.0458" y2="15.2964" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint11_linear_6503_7105" x1="13.6936" y1="2.85547" x2="12.8816" y2="3.27031" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint12_linear_6503_7105" x1="15.0447" y1="2.85547" x2="14.2331" y2="3.26986" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint13_linear_6503_7105" x1="21.1459" y1="2.85547" x2="20.6829" y2="4.74624" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        </defs>
                        </svg>


                </div>
                <div class="mx-2 mt-2 text-sm">
                    <b class="text-base">10+ years of experience</b>
                    <p class="mt-2">Over 40 travel advisors with over 10 years experience in the travel industry.</p>
                </div>
            </div>
            <div class="flex">
                <div class="flex-shrink-0 bg-white rounded-full flex items-center justify-center w-10 h-10">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.25 14.8879C20.2499 14.2315 19.9891 13.6021 19.525 13.1381C19.0609 12.674 18.4315 12.4132 17.7752 12.4131H6.22523C5.9002 12.413 5.57834 12.477 5.27804 12.6013C4.97773 12.7257 4.70486 12.908 4.47501 13.1378C4.24515 13.3676 4.06282 13.6404 3.93842 13.9407C3.81403 14.241 3.75 14.5628 3.75 14.8879V15.7131C3.75 18.9651 6.81923 22.3131 12 22.3131C17.1808 22.3131 20.25 18.9651 20.25 15.7131V14.8879ZM5.4 14.8879C5.40012 14.6691 5.48712 14.4593 5.64187 14.3046C5.79662 14.15 6.00645 14.0631 6.22523 14.0631H17.7752C17.9939 14.0632 18.2036 14.1501 18.3583 14.3048C18.5129 14.4594 18.5999 14.6692 18.6 14.8879V15.7131C18.6 18.0854 16.2369 20.6631 12 20.6631C7.76308 20.6631 5.4 18.0854 5.4 15.7131V14.8879Z" fill="url(#paint0_linear_6503_7125)"/>
                        <path d="M16.5403 6.22516C16.5401 5.24714 16.224 4.29535 15.6389 3.51162C15.0539 2.72789 14.2313 2.15417 13.2937 1.87595C12.3561 1.59773 11.3537 1.6299 10.4358 1.96768C9.51799 2.30545 8.73386 2.93075 8.20028 3.75039H5.40336C5.29501 3.75033 5.18771 3.77161 5.08759 3.81304C4.98747 3.85446 4.89649 3.9152 4.81986 3.99179C4.74322 4.06839 4.68243 4.15933 4.64095 4.25942C4.59947 4.35952 4.57812 4.46681 4.57813 4.57516V7.87516C4.57813 7.90146 4.57812 7.92639 4.58136 7.95131H4.57813V9.52516C4.57813 9.85021 4.64215 10.1721 4.76654 10.4724C4.89093 10.7727 5.07326 11.0456 5.3031 11.2754C5.53295 11.5053 5.80582 11.6876 6.10613 11.812C6.40643 11.9364 6.7283 12.0004 7.05336 12.0004H7.69166C7.86148 12.0001 8.02847 11.9569 8.17722 11.875C8.32597 11.7931 8.45169 11.6751 8.54277 11.5317C8.63385 11.3884 8.68735 11.2245 8.69833 11.055C8.70932 10.8856 8.67743 10.7161 8.60562 10.5622C8.53381 10.4083 8.42439 10.275 8.28746 10.1746C8.15053 10.0741 7.9905 10.0098 7.82215 9.98757C7.65381 9.96531 7.48256 9.98583 7.32424 10.0472C7.16592 10.1086 7.02561 10.2089 6.91628 10.3388C6.72399 10.3063 6.54942 10.2068 6.42354 10.0578C6.29765 9.90889 6.22859 9.72018 6.22859 9.52516V8.70039H6.64074C7.11273 8.70072 7.57489 8.56552 7.97228 8.31085C8.43923 9.21456 9.1963 9.93515 10.1219 10.3569C11.0476 10.7787 12.0881 10.8772 13.0765 10.6367C14.0648 10.3961 14.9437 9.83036 15.5719 9.03033C16.2002 8.2303 16.5409 7.24237 16.5403 6.22516ZM9.11551 6.22516C9.11551 5.45931 9.41974 4.72484 9.96127 4.1833C10.5028 3.64177 11.2373 3.33754 12.0031 3.33754C12.769 3.33754 13.5034 3.64177 14.045 4.1833C14.5865 4.72484 14.8907 5.45931 14.8907 6.22516C14.8907 6.991 14.5865 7.72548 14.045 8.26701C13.5034 8.80854 12.769 9.11277 12.0031 9.11277C11.2373 9.11277 10.5028 8.80854 9.96127 8.26701C9.41974 7.72548 9.11551 6.991 9.11551 6.22516ZM7.46551 6.22516C7.46551 6.44394 7.37863 6.65377 7.22397 6.80852C7.06931 6.96327 6.85953 7.05026 6.64074 7.05039H6.22813V5.40039H7.46551V6.22516Z" fill="url(#paint1_linear_6503_7125)"/>
                        <defs>
                        <linearGradient id="paint0_linear_6503_7125" x1="20.25" y1="12.4131" x2="5.78846" y2="24.7103" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_6503_7125" x1="16.5407" y1="1.68848" x2="3.16167" y2="9.60723" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#590C32"/>
                        <stop offset="1" stop-color="#9D1D5A"/>
                        </linearGradient>
                        </defs>
                        </svg>

                </div>
                <div class="mx-2 mt-2 text-sm">
                    <b class="text-base mb-1">24/7 live support</b>
                    <p class="mt-2">24/7 assistance available from anywhere in the world at any stage of your trip.</p>
                </div>
            </div>
    </div>
</div>

<?=\backend\components\SubscribeWidget::widget([])?>

<!-- Activity name for this tag: IO285823_Page Views -->

<script>

    (function() {

        var a = String(Math.random()) * 10000000000000;

        new Image().src = 'https://pubads.g.doubleclick.net/activity;xsp=4794320;ord='+ a +'?';

    })();

    function openJivoCallButton() {
        const jivoCallbackBtn = document.querySelector('jdiv.__jivoCallbackBtn')
        if(jivoCallbackBtn){
            jivoCallbackBtn.click()
        }
    }

    function jivo_onLoadCallback() {
        document.getElementById('jivo_callback_btn').addEventListener("click", openJivoCallButton);
    }

</script>

<noscript>

    <img loading=lazy src='https://pubads.g.doubleclick.net/activity;xsp=4794320;ord=1?' width=1 height=1 border=0>

</noscript>
