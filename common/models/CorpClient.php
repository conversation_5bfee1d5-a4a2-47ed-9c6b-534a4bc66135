<?php

namespace common\models;

use EmailTemplate\Generator;
use common\components\JsonHelper;
use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\web\TooManyRequestsHttpException;
use function Webmozart\Assert\Tests\StaticAnalysis\null;

/**
 * This is the model class for table "{{%corp_clients}}".
 *
 * @property int $id
 * @property int $created_at
 * @property int $updated_at
 * @property string $fullName
 * @property string $email
 * @property string $phone
 * @property string $company
 * @property boolean $is_sync
 * @property int $travellers
 */
class CorpClient extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%corp_clients}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            TimestampBehavior::class,
        ];
    }


    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created at',
            'updated_at' => 'Updated at',
            'fullName' => 'Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'company' => 'Company',
            'travellers' => 'Nr of travellers',
            'is_sync' => 'Is sync'
        ];
    }

}
