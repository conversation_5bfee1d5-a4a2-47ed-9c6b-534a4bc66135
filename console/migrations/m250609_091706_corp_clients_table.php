<?php

use yii\db\Migration;

class m250609_091706_corp_clients_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%corp_clients}}', [
            'id' => $this->primaryKey(),
            'created_at' => $this->integer()->notNull()->defaultValue(0),
            'updated_at' => $this->integer()->notNull()->defaultValue(0),
            'fullName' => $this->string(150)->notNull(),
            'email' => $this->string(150)->notNull(),
            'phone' => $this->string(150)->notNull(),
            'company' => $this->string(150)->notNull(),
            'travellers' => $this->integer()->notNull(),
            'is_sync' => $this->boolean(false)->notNull(),
        ]);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%corp_clients}}');

        return true;
    }
}
