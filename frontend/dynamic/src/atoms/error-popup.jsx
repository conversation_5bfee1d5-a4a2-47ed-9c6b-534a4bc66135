import { createRef, useEffect } from 'react'
import styled, { css } from 'styled-components'

import { useOnClickOutside } from '../hooks'
import { ReactComponent as CloseIcon } from '../icons/close.svg'
const keys = [32, 33, 34, 35, 36, 37, 38, 39, 40]

function preventDefault(e) {
  e.preventDefault()
}

function preventDefaultForScrollKeys(e) {
  if (keys.includes(e.keyCode)) {
    preventDefault(e)
    return false
  }
}

var supportsPassive = false
try {
  window.addEventListener(
    'test',
    null,
    Object.defineProperty({}, 'passive', {
      get: function () {
        supportsPassive = true
      }
    })
  )
} catch (e) {}

var wheelOpt = supportsPassive ? { passive: false } : false
var wheelEvent =
  'onwheel' in document.createElement('div') ? 'wheel' : 'mousewheel'

export const ErrorPopup = ({ errors, clearErrors }) => {
  const ref = createRef()

  // Функция для обработки ошибок, включая вложенные в itineraries
  const processErrors = () => {
    const errorMessages = [];

    // Обработка обычных ошибок
    Object.entries(errors).forEach(([key, error]) => {
      // Пропускаем itineraries, обработаем их отдельно
      if (key === 'itineraries') return;

      if (error?.message) {
        errorMessages.push(error.message);
      }

      if (error?.label?.message) {
        errorMessages.push(error.label.message);
      }
    });

    // Обработка ошибок itineraries
    if (errors.itineraries) {
      // Создаем Set для уникальных сообщений об ошибках
      const uniqueItineraryErrors = new Set();

      // Проходим по всем маршрутам
      if (Array.isArray(errors.itineraries)) {
        errors.itineraries.forEach((itinerary, index) => {
          Object.entries(itinerary || {}).forEach(([field, error]) => {
            if (error?.message) {
              uniqueItineraryErrors.add(`Flight ${index + 1}: ${error.message}`);
            }

            if (error?.label?.message) {
              uniqueItineraryErrors.add(`Flight ${index + 1}: ${error.label.message}`);
            }
          });
        });
      }

      // Добавляем уникальные ошибки маршрутов
      errorMessages.push(...uniqueItineraryErrors);
    }

    return errorMessages;
  };

  const errorMessages = processErrors();

  useEffect(() => {
    if (errorMessages.length < 14) {
      window.addEventListener('DOMMouseScroll', preventDefault, false) // older FF
      window.addEventListener(wheelEvent, preventDefault, wheelOpt) // modern desktop
      window.addEventListener('touchmove', preventDefault, wheelOpt) // mobile
      window.addEventListener('keydown', preventDefaultForScrollKeys, false)
    }

    return () => {
      window.removeEventListener('DOMMouseScroll', preventDefault, false) // older FF
      window.removeEventListener(wheelEvent, preventDefault, wheelOpt) // modern desktop
      window.removeEventListener('touchmove', preventDefault, wheelOpt) // mobile
      window.removeEventListener('keydown', preventDefaultForScrollKeys, false)
    }
  })
  useOnClickOutside(ref, () => clearErrors())
  return (
    <StyledBackDrop>
      <StyledContainer ref={ref}>

        <StyledErrorsList>
          {errorMessages.map((message, index) => (
            <StyledErrorMessage key={index}>{message}</StyledErrorMessage>
          ))}
        </StyledErrorsList>


        <ButtonGroup>
          <DeclineBtn onClick={() => clearErrors()}>Ok</DeclineBtn>
        </ButtonGroup>

        <StyledCloseBtn onClick={() => clearErrors()}>
          <CloseIcon />
        </StyledCloseBtn>
      </StyledContainer>
    </StyledBackDrop>
  )
}

const StyledBackDrop = styled.div`
  ${({ theme }) => css`
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 100;
    position: fixed;

    display: flex;
    justify-content: center;
    align-items: center;

    background-color: rgba(0, 0, 0, 0.5);
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
    transition-duration: 75ms;
    transition-property: opacity;
  `}
`

const StyledErrorsList = styled.div`
  ${({ theme }) => css`
    display: flex;
    flex-direction: column;
    min-height: 300px;
    max-height: 65vh;
    width: 100%;
    overflow-y: auto;

  `}
`

const StyledContainer = styled.div`
  ${({ theme }) => css`
    width: 37.5rem;
    background: #ffffff;
    border-radius: 36px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;
    padding: 4rem;
    position: relative;
    margin: 0.625rem;
  `}
`


const StyledErrorMessage = styled.p`
  ${({ theme }) => css`
    //color: #d32f2f;
    font-size: 14px;
    margin: 8px 0;
    text-align: center;
    font-weight: 500;

    &:not(:last-child) {
      //border-bottom: 1px solid #f5f5f5;
      padding-bottom: 8px;
    }
  `}
`

const StyledCloseBtn = styled.button`
  ${({ theme }) => css`
    position: absolute;
    top: 25px;
    right: 25px;

    width: 45px;
    height: 45px;
    background: #dbeafb;
    border-radius: 39px;

    display: flex;
    justify-content: center;
    align-items: center;
  `}
`

const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 35px;
`

const ButtonBase = styled.button`
  font-family: sbc-font;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 120%;

  padding: 1rem;

  text-align: center;

  width: 145px;

  border-radius: 50px;
  box-shadow: 0px 4px 15px rgba(68, 85, 234, 0.1);
`

const AcceptBtn = styled(ButtonBase)`
  color: #fff;
  background: #00c908;
  margin-bottom: 22px;
`

const DeclineBtn = styled(ButtonBase)`
  color: #4a94ec;
  background: rgba(74, 148, 236, 0.1);
`
