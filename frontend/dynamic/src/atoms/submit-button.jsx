import styled, { css } from 'styled-components'

import { breakpoints } from '../constants/breakpoints'
import { useWindowSize } from '../hooks'

export const SubmitButton = ({ submit, hideIcon = false }) => {
  const redirectPost = (url, data) => {
    data = {
      ...data,
      itineraries: data.itineraries.map((itinerary) => {
        return {
          ...itinerary,
          departure: correctDate(itinerary.departure),
          return: itinerary.return ? correctDate(itinerary.return) : null
        }
      })
    }
    // data = {...data, departure: correctDate(data.departure), return: data.return ? correctDate(data.return) : null}

    const form = document.createElement('form')
    document.body.appendChild(form)
    form.method = 'post'
    form.action = url

    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = 'data'
    input.value = JSON.stringify(data)
    form.appendChild(input)
    form.submit()
  }

  const onClick = (data) => {
    try {
      window.localStorage.setItem('form-data', JSON.stringify(data))
    } finally {
      redirectPost('/offer/search/', data)
    }
  }

  const { width } = useWindowSize()
  const isMobile = width <= breakpoints.xl

  const correctDate = (date) => {
    const newDay = new Date()
    newDay.setHours(12, 0, 0, 0)
    newDay.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate())
    return newDay
  }

  return (
    <>
      {isMobile || hideIcon ? (
        <StyledSubmitTextButton onClick={submit(onClick)}>
          Search
        </StyledSubmitTextButton>
      ) : (
        <StyledSubmitIconButton onClick={submit(onClick)}>
          <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.4393 18.0607C17.0251 18.6464 17.9749 18.6464 18.5607 18.0607C19.1464 17.4749 19.1464 16.5251 18.5607 15.9393L16.4393 18.0607ZM12.4393 14.0607L16.4393 18.0607L18.5607 15.9393L14.5607 11.9393L12.4393 14.0607Z"
              fill="#F4F6F7"
            />
            <circle cx="8" cy="8" r="6.5" stroke="#F4F6F7" strokeWidth="3" />
          </svg>
        </StyledSubmitIconButton>
      )}
    </>
  )
}

const StyledSubmitTextButton = styled.button`
  ${({ theme }) => css`
    display: flex;
    //width: 240px;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
    --tw-gradient-from: #590c32;
    --tw-gradient-stops: var(--tw-gradient-from),
      var(--tw-gradient-to, rgba(89, 12, 50, 0));
    --tw-gradient-to: #9d1d5a;
    font-family: sbc-font;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 110%; /* 13.2px */
    letter-spacing: 1.2px;
    text-transform: uppercase;
    padding: 0.75rem 1.5rem;
    border-radius: 56px;
    color: #fff;
      @media ${theme.breakpoints.lg} {
          height: 56px;
      }
  `}
`

const StyledSubmitIconButton = styled.button.attrs({ type: 'submit' })`
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
  --tw-gradient-from: #590c32;
  --tw-gradient-stops: var(--tw-gradient-from),
    var(--tw-gradient-to, rgba(89, 12, 50, 0));
  --tw-gradient-to: #9d1d5a;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  height: 2.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgba(244, 246, 247, var(--tw-text-opacity));
  text-transform: uppercase;
  letter-spacing: 0.1em;
  width: 100%;

  @media (min-width: 1280px) {
    height: 3.75rem;
    width: 3.75rem;
  }
`
