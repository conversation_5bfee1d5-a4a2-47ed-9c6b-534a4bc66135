import * as yup from 'yup'

const itineraries = [{
  from: {
    label: '',
    isValid: false
  },
  to: {
    label: '',
    isValid: false
  },
  departure: null,
  return: null
}]

const passengers = {
  adults: 1,
  childrens: 0,
  infants: 0
}

const tripClass = 'business'

const tripType = 1

export const defaultValues = {
  itineraries,
  passengers, // keep it as separate obj
  tripClass,
  tripType
}

export const validationSchema = yup.object().shape({
  itineraries: yup.array().of(
    yup.object().shape({
      from: yup.object().shape({
        label: yup.string().required(`Please enter a "From" city.`).min(1)
      }),
      to: yup.object().shape({
        label: yup.string().required(`Please enter a "To" city.`).min(1)
      }),
      departure: yup
        .date()
        .nullable(true)
        .required(`Please enter a "Departure" date.`),
      return: yup
        .date()
        .nullable(true)
    })
  ).min(1, 'At least one itinerary is required'),
  passengers: yup.object().shape({
    adults: yup.number().min(1).required(),
    childrens: yup.number().min(0).required(),
    infants: yup.number().min(0).required()
  }),
  tripClass: yup.string().oneOf(['premium', 'business', 'first']).required(),
  tripType: yup.number().oneOf([0, 1, 2]).required()
})
.test('from-to-not-equal', 'From and To cities cannot be the same', function(value) {
  return value.itineraries.every(itinerary =>
    !itinerary.from.code || !itinerary.to.code || itinerary.from.code !== itinerary.to.code
  )
})
.test('roundtrip-return-date', 'Please enter a "Return" date for round trip', function(value) {
  if (value.tripType === 0) {
    return value.itineraries.every(itinerary => itinerary.return !== null);
  }
  return true;
})
