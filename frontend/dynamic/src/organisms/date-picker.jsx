import { useState, createRef, useEffect } from 'react'
import DatePicker from 'react-datepicker'
import { useWatch } from 'react-hook-form'
import styled, { css } from 'styled-components'

import { Label, CheckboxSlider, StyledDatePickerWrapper } from '../atoms'
import { breakpoints } from '../constants/breakpoints'
import 'react-datepicker/dist/react-datepicker.css'
import { useOnClickOutside, useWindowSize } from '../hooks'
import { ReactComponent as ClearIcon } from '../icons/clear.svg'

export const DatePickerField = ({ setValue, getValues, control, itineraryIndex = 0 }) => {
  const ref = createRef()

  const [isOpen, setIsOpen] = useState(false)
  const [isOneWay, toggleOneWay] = useState(true)

  const [isSelectingStart, setSelectingStart] = useState(false)
  const [isSelectingEnd, setSelectingEnd] = useState(false)

    const startDate = useWatch({
        control,
        name: `itineraries[${itineraryIndex}].departure`,
    })

    const endDate = useWatch({
        control,
        name: `itineraries[${itineraryIndex}].return`,
    })

    const { width } = useWindowSize()
  const isMobile = width <= breakpoints.xl

  const tripType = useWatch({
    name: 'tripType',
    control
  })

  useEffect(() => {
    const isOneWay = tripType === 1

    toggleOneWay(isOneWay)

    if (isOneWay) {
      setValue(`itineraries[${itineraryIndex}].return`, null)
    }
  }, [tripType, setValue, itineraryIndex])

  useOnClickOutside(ref, () => {
    setIsOpen(false)
    setSelectingStart(false)
    setSelectingEnd(false)
  })

    const onChangeDay = (date) => {
        if (tripType === 2) {
            setValue(`itineraries[${itineraryIndex}].departure`, date)
            setIsOpen(false)
            return
        }

        if (isSelectingStart) {
            if (endDate !== null && date > endDate) {
                setValue(`itineraries[${itineraryIndex}].return`, null)
            }

            setValue(`itineraries[${itineraryIndex}].departure`, date)

            if (isOneWay) {
                setIsOpen(false)
                setSelectingStart(false)
            } else {
                setSelectingStart(false)
                setSelectingEnd(true)
            }
        } else {
            if (startDate === null) {
                const today = new Date()
                setValue(`itineraries[${itineraryIndex}].departure`, today)
            }

            if (startDate !== null && date < startDate) {
                setValue(`itineraries[${itineraryIndex}].departure`, date)
                return
            }

            setValue(`itineraries[${itineraryIndex}].return`, date)
            setSelectingEnd(false)
            setIsOpen(false)
        }
    }


    const renderDayContents = (day, date) =>
    day ? <span className="day-number">{day}</span> : null

  return (
    <>
      <StyledRelativeWrapper className="relative">
        <StyledAbsoluteWrapper ref={ref} isOpen={isOpen} tripType={tripType}>
          <StyledPopupHeader isOpen={isOpen} withAdditionalPadding={tripType === 2}>
            <GridRow cols={!isMobile && isOpen ? (tripType === 2 ? 2 : 4) : 2} className="row" tripType={tripType}>
              {!isMobile && isOpen && tripType !== 2 && <div />}
              <GridItem
                isOpen={isOpen}
                active={tripType === 2 ? false : isSelectingStart}
                className="row-item"
                tripType={tripType}
                itineraryIndex={itineraryIndex}
                onClick={() => {
                  setIsOpen(true)
                  if (tripType !== 2) {
                    setSelectingStart(true)
                    setSelectingEnd(false)
                  }
                }}
              >
                <StyledDateInput>
                  <Label text="Departure" />
                  <StyledDatePickerWrapper active={tripType === 2 ? false : isSelectingStart}>
                    <DatePicker
                      selected={startDate}
                      dateFormat="d MMM yyyy"
                      placeholderText="Select date"
                      popperClassName="d-none"
                      readOnly
                    />
                  </StyledDatePickerWrapper>
                </StyledDateInput>
                <ImageWrapper>
                  <svg
                    width="18"
                    height="19"
                    viewBox="0 0 18 19"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <mask id="path-1-inside-1" fill="white">
                      <path d="M15 2H14V1C14 0.734784 13.8946 0.48043 13.7071 0.292893C13.5196 0.105357 13.2652 0 13 0C12.7348 0 12.4804 0.105357 12.2929 0.292893C12.1054 0.48043 12 0.734784 12 1V2H6V1C6 0.734784 5.89464 0.48043 5.70711 0.292893C5.51957 0.105357 5.26522 0 5 0C4.73478 0 4.48043 0.105357 4.29289 0.292893C4.10536 0.48043 4 0.734784 4 1V2H3C2.20435 2 1.44129 2.31607 0.87868 2.87868C0.316071 3.44129 0 4.20435 0 5V16C0 16.7956 0.316071 17.5587 0.87868 18.1213C1.44129 18.6839 2.20435 19 3 19H15C15.7956 19 16.5587 18.6839 17.1213 18.1213C17.6839 17.5587 18 16.7956 18 16V5C18 4.20435 17.6839 3.44129 17.1213 2.87868C16.5587 2.31607 15.7956 2 15 2ZM5 16C4.80222 16 4.60888 15.9414 4.44443 15.8315C4.27998 15.7216 4.15181 15.5654 4.07612 15.3827C4.00043 15.2 3.98063 14.9989 4.01921 14.8049C4.0578 14.6109 4.15304 14.4327 4.29289 14.2929C4.43275 14.153 4.61093 14.0578 4.80491 14.0192C4.99889 13.9806 5.19996 14.0004 5.38268 14.0761C5.56541 14.1518 5.72159 14.28 5.83147 14.4444C5.94135 14.6089 6 14.8022 6 15C6 15.2652 5.89464 15.5196 5.70711 15.7071C5.51957 15.8946 5.26522 16 5 16ZM5 12C4.80222 12 4.60888 11.9414 4.44443 11.8315C4.27998 11.7216 4.15181 11.5654 4.07612 11.3827C4.00043 11.2 3.98063 10.9989 4.01921 10.8049C4.0578 10.6109 4.15304 10.4327 4.29289 10.2929C4.43275 10.153 4.61093 10.0578 4.80491 10.0192C4.99889 9.98063 5.19996 10.0004 5.38268 10.0761C5.56541 10.1518 5.72159 10.28 5.83147 10.4444C5.94135 10.6089 6 10.8022 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12ZM9 16C8.80222 16 8.60888 15.9414 8.44443 15.8315C8.27998 15.7216 8.15181 15.5654 8.07612 15.3827C8.00043 15.2 7.98063 14.9989 8.01921 14.8049C8.0578 14.6109 8.15304 14.4327 8.29289 14.2929C8.43275 14.153 8.61093 14.0578 8.80491 14.0192C8.99889 13.9806 9.19996 14.0004 9.38268 14.0761C9.56541 14.1518 9.72159 14.28 9.83147 14.4444C9.94135 14.6089 10 14.8022 10 15C10 15.2652 9.89464 15.5196 9.70711 15.7071C9.51957 15.8946 9.26522 16 9 16ZM9 12C8.80222 12 8.60888 11.9414 8.44443 11.8315C8.27998 11.7216 8.15181 11.5654 8.07612 11.3827C8.00043 11.2 7.98063 10.9989 8.01921 10.8049C8.0578 10.6109 8.15304 10.4327 8.29289 10.2929C8.43275 10.153 8.61093 10.0578 8.80491 10.0192C8.99889 9.98063 9.19996 10.0004 9.38268 10.0761C9.56541 10.1518 9.72159 10.28 9.83147 10.4444C9.94135 10.6089 10 10.8022 10 11C10 11.2652 9.89464 11.5196 9.70711 11.7071C9.51957 11.8946 9.26522 12 9 12ZM13 12C12.8022 12 12.6089 11.9414 12.4444 11.8315C12.28 11.7216 12.1518 11.5654 12.0761 11.3827C12.0004 11.2 11.9806 10.9989 12.0192 10.8049C12.0578 10.6109 12.153 10.4327 12.2929 10.2929C12.4327 10.153 12.6109 10.0578 12.8049 10.0192C12.9989 9.98063 13.2 10.0004 13.3827 10.0761C13.5654 10.1518 13.7216 10.28 13.8315 10.4444C13.9414 10.6089 14 10.8022 14 11C14 11.2652 13.8946 11.5196 13.7071 11.7071C13.5196 11.8946 13.2652 12 13 12ZM16 8H2V5C2 4.73478 2.10536 4.48043 2.29289 4.29289C2.48043 4.10536 2.73478 4 3 4H4V5C4 5.26522 4.10536 5.51957 4.29289 5.70711C4.48043 5.89464 4.73478 6 5 6C5.26522 6 5.51957 5.89464 5.70711 5.70711C5.89464 5.51957 6 5.26522 6 5V4H12V5C12 5.26522 12.1054 5.51957 12.2929 5.70711C12.4804 5.89464 12.7348 6 13 6C13.2652 6 13.5196 5.89464 13.7071 5.70711C13.8946 5.51957 14 5.26522 14 5V4H15C15.2652 4 15.5196 4.10536 15.7071 4.29289C15.8946 4.48043 16 4.73478 16 5V8Z" />
                    </mask>
                    <path
                      d="M15 2H14V1C14 0.734784 13.8946 0.48043 13.7071 0.292893C13.5196 0.105357 13.2652 0 13 0C12.7348 0 12.4804 0.105357 12.2929 0.292893C12.1054 0.48043 12 0.734784 12 1V2H6V1C6 0.734784 5.89464 0.48043 5.70711 0.292893C5.51957 0.105357 5.26522 0 5 0C4.73478 0 4.48043 0.105357 4.29289 0.292893C4.10536 0.48043 4 0.734784 4 1V2H3C2.20435 2 1.44129 2.31607 0.87868 2.87868C0.316071 3.44129 0 4.20435 0 5V16C0 16.7956 0.316071 17.5587 0.87868 18.1213C1.44129 18.6839 2.20435 19 3 19H15C15.7956 19 16.5587 18.6839 17.1213 18.1213C17.6839 17.5587 18 16.7956 18 16V5C18 4.20435 17.6839 3.44129 17.1213 2.87868C16.5587 2.31607 15.7956 2 15 2ZM5 16C4.80222 16 4.60888 15.9414 4.44443 15.8315C4.27998 15.7216 4.15181 15.5654 4.07612 15.3827C4.00043 15.2 3.98063 14.9989 4.01921 14.8049C4.0578 14.6109 4.15304 14.4327 4.29289 14.2929C4.43275 14.153 4.61093 14.0578 4.80491 14.0192C4.99889 13.9806 5.19996 14.0004 5.38268 14.0761C5.56541 14.1518 5.72159 14.28 5.83147 14.4444C5.94135 14.6089 6 14.8022 6 15C6 15.2652 5.89464 15.5196 5.70711 15.7071C5.51957 15.8946 5.26522 16 5 16ZM5 12C4.80222 12 4.60888 11.9414 4.44443 11.8315C4.27998 11.7216 4.15181 11.5654 4.07612 11.3827C4.00043 11.2 3.98063 10.9989 4.01921 10.8049C4.0578 10.6109 4.15304 10.4327 4.29289 10.2929C4.43275 10.153 4.61093 10.0578 4.80491 10.0192C4.99889 9.98063 5.19996 10.0004 5.38268 10.0761C5.56541 10.1518 5.72159 10.28 5.83147 10.4444C5.94135 10.6089 6 10.8022 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12ZM9 16C8.80222 16 8.60888 15.9414 8.44443 15.8315C8.27998 15.7216 8.15181 15.5654 8.07612 15.3827C8.00043 15.2 7.98063 14.9989 8.01921 14.8049C8.0578 14.6109 8.15304 14.4327 8.29289 14.2929C8.43275 14.153 8.61093 14.0578 8.80491 14.0192C8.99889 13.9806 9.19996 14.0004 9.38268 14.0761C9.56541 14.1518 9.72159 14.28 9.83147 14.4444C9.94135 14.6089 10 14.8022 10 15C10 15.2652 9.89464 15.5196 9.70711 15.7071C9.51957 15.8946 9.26522 16 9 16ZM9 12C8.80222 12 8.60888 11.9414 8.44443 11.8315C8.27998 11.7216 8.15181 11.5654 8.07612 11.3827C8.00043 11.2 7.98063 10.9989 8.01921 10.8049C8.0578 10.6109 8.15304 10.4327 8.29289 10.2929C8.43275 10.153 8.61093 10.0578 8.80491 10.0192C8.99889 9.98063 9.19996 10.0004 9.38268 10.0761C9.56541 10.1518 9.72159 10.28 9.83147 10.4444C9.94135 10.6089 10 10.8022 10 11C10 11.2652 9.89464 11.5196 9.70711 11.7071C9.51957 11.8946 9.26522 12 9 12ZM13 12C12.8022 12 12.6089 11.9414 12.4444 11.8315C12.28 11.7216 12.1518 11.5654 12.0761 11.3827C12.0004 11.2 11.9806 10.9989 12.0192 10.8049C12.0578 10.6109 12.153 10.4327 12.2929 10.2929C12.4327 10.153 12.6109 10.0578 12.8049 10.0192C12.9989 9.98063 13.2 10.0004 13.3827 10.0761C13.5654 10.1518 13.7216 10.28 13.8315 10.4444C13.9414 10.6089 14 10.8022 14 11C14 11.2652 13.8946 11.5196 13.7071 11.7071C13.5196 11.8946 13.2652 12 13 12ZM16 8H2V5C2 4.73478 2.10536 4.48043 2.29289 4.29289C2.48043 4.10536 2.73478 4 3 4H4V5C4 5.26522 4.10536 5.51957 4.29289 5.70711C4.48043 5.89464 4.73478 6 5 6C5.26522 6 5.51957 5.89464 5.70711 5.70711C5.89464 5.51957 6 5.26522 6 5V4H12V5C12 5.26522 12.1054 5.51957 12.2929 5.70711C12.4804 5.89464 12.7348 6 13 6C13.2652 6 13.5196 5.89464 13.7071 5.70711C13.8946 5.51957 14 5.26522 14 5V4H15C15.2652 4 15.5196 4.10536 15.7071 4.29289C15.8946 4.48043 16 4.73478 16 5V8Z"
                      strokeWidth="1rem"
                      mask="url(#path-1-inside-1)"
                    />
                    <defs>
                      <linearGradient
                        id="gradient"
                        x1="18"
                        y1="-1.13249e-06"
                        x2="-4.03554"
                        y2="10.6509"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop stopColor="#590C32" />
                        <stop offset="1" stopColor="#9D1D5A" />
                      </linearGradient>
                    </defs>
                  </svg>
                </ImageWrapper>
              </GridItem>

              { tripType !== 2 && (
                <GridItem
                  isOpen={isOpen}
                  active={isSelectingEnd}
                  className="row-item"
                  showClearBtn={isMobile}
                  tripType={tripType}
                  itineraryIndex={itineraryIndex}
                  onClick={() => {
                    setValue('tripType', 0)
                    setIsOpen(true)
                    setSelectingStart(false)
                    setSelectingEnd(true)
                  }}
                >
                  <StyledDateInput>
                    <Label text="Return" />
                    <StyledDatePickerWrapper
                      isOneWay={isOneWay}
                      active={isSelectingEnd}
                    >
                      <DatePicker
                        selected={endDate}
                        dateFormat="d MMM yyyy"
                        placeholderText="+ Add return"
                        popperClassName="d-none"
                        readOnly
                      />
                    </StyledDatePickerWrapper>
                  </StyledDateInput>
                  {endDate === null && !isSelectingEnd ? (
                    <ImageWrapper>
                      <svg
                        width="18"
                        height="19"
                        viewBox="0 0 18 19"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <mask id="path-1-inside-1" fill="white">
                          <path d="M15 2H14V1C14 0.734784 13.8946 0.48043 13.7071 0.292893C13.5196 0.105357 13.2652 0 13 0C12.7348 0 12.4804 0.105357 12.2929 0.292893C12.1054 0.48043 12 0.734784 12 1V2H6V1C6 0.734784 5.89464 0.48043 5.70711 0.292893C5.51957 0.105357 5.26522 0 5 0C4.73478 0 4.48043 0.105357 4.29289 0.292893C4.10536 0.48043 4 0.734784 4 1V2H3C2.20435 2 1.44129 2.31607 0.87868 2.87868C0.316071 3.44129 0 4.20435 0 5V16C0 16.7956 0.316071 17.5587 0.87868 18.1213C1.44129 18.6839 2.20435 19 3 19H15C15.7956 19 16.5587 18.6839 17.1213 18.1213C17.6839 17.5587 18 16.7956 18 16V5C18 4.20435 17.6839 3.44129 17.1213 2.87868C16.5587 2.31607 15.7956 2 15 2ZM5 16C4.80222 16 4.60888 15.9414 4.44443 15.8315C4.27998 15.7216 4.15181 15.5654 4.07612 15.3827C4.00043 15.2 3.98063 14.9989 4.01921 14.8049C4.0578 14.6109 4.15304 14.4327 4.29289 14.2929C4.43275 14.153 4.61093 14.0578 4.80491 14.0192C4.99889 13.9806 5.19996 14.0004 5.38268 14.0761C5.56541 14.1518 5.72159 14.28 5.83147 14.4444C5.94135 14.6089 6 14.8022 6 15C6 15.2652 5.89464 15.5196 5.70711 15.7071C5.51957 15.8946 5.26522 16 5 16ZM5 12C4.80222 12 4.60888 11.9414 4.44443 11.8315C4.27998 11.7216 4.15181 11.5654 4.07612 11.3827C4.00043 11.2 3.98063 10.9989 4.01921 10.8049C4.0578 10.6109 4.15304 10.4327 4.29289 10.2929C4.43275 10.153 4.61093 10.0578 4.80491 10.0192C4.99889 9.98063 5.19996 10.0004 5.38268 10.0761C5.56541 10.1518 5.72159 10.28 5.83147 10.4444C5.94135 10.6089 6 10.8022 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12ZM9 16C8.80222 16 8.60888 15.9414 8.44443 15.8315C8.27998 15.7216 8.15181 15.5654 8.07612 15.3827C8.00043 15.2 7.98063 14.9989 8.01921 14.8049C8.0578 14.6109 8.15304 14.4327 8.29289 14.2929C8.43275 14.153 8.61093 14.0578 8.80491 14.0192C8.99889 13.9806 9.19996 14.0004 9.38268 14.0761C9.56541 14.1518 9.72159 14.28 9.83147 14.4444C9.94135 14.6089 10 14.8022 10 15C10 15.2652 9.89464 15.5196 9.70711 15.7071C9.51957 15.8946 9.26522 16 9 16ZM9 12C8.80222 12 8.60888 11.9414 8.44443 11.8315C8.27998 11.7216 8.15181 11.5654 8.07612 11.3827C8.00043 11.2 7.98063 10.9989 8.01921 10.8049C8.0578 10.6109 8.15304 10.4327 8.29289 10.2929C8.43275 10.153 8.61093 10.0578 8.80491 10.0192C8.99889 9.98063 9.19996 10.0004 9.38268 10.0761C9.56541 10.1518 9.72159 10.28 9.83147 10.4444C9.94135 10.6089 10 10.8022 10 11C10 11.2652 9.89464 11.5196 9.70711 11.7071C9.51957 11.8946 9.26522 12 9 12ZM13 12C12.8022 12 12.6089 11.9414 12.4444 11.8315C12.28 11.7216 12.1518 11.5654 12.0761 11.3827C12.0004 11.2 11.9806 10.9989 12.0192 10.8049C12.0578 10.6109 12.153 10.4327 12.2929 10.2929C12.4327 10.153 12.6109 10.0578 12.8049 10.0192C12.9989 9.98063 13.2 10.0004 13.3827 10.0761C13.5654 10.1518 13.7216 10.28 13.8315 10.4444C13.9414 10.6089 14 10.8022 14 11C14 11.2652 13.8946 11.5196 13.7071 11.7071C13.5196 11.8946 13.2652 12 13 12ZM16 8H2V5C2 4.73478 2.10536 4.48043 2.29289 4.29289C2.48043 4.10536 2.73478 4 3 4H4V5C4 5.26522 4.10536 5.51957 4.29289 5.70711C4.48043 5.89464 4.73478 6 5 6C5.26522 6 5.51957 5.89464 5.70711 5.70711C5.89464 5.51957 6 5.26522 6 5V4H12V5C12 5.26522 12.1054 5.51957 12.2929 5.70711C12.4804 5.89464 12.7348 6 13 6C13.2652 6 13.5196 5.89464 13.7071 5.70711C13.8946 5.51957 14 5.26522 14 5V4H15C15.2652 4 15.5196 4.10536 15.7071 4.29289C15.8946 4.48043 16 4.73478 16 5V8Z" />
                        </mask>
                        <path
                          d="M15 2H14V1C14 0.734784 13.8946 0.48043 13.7071 0.292893C13.5196 0.105357 13.2652 0 13 0C12.7348 0 12.4804 0.105357 12.2929 0.292893C12.1054 0.48043 12 0.734784 12 1V2H6V1C6 0.734784 5.89464 0.48043 5.70711 0.292893C5.51957 0.105357 5.26522 0 5 0C4.73478 0 4.48043 0.105357 4.29289 0.292893C4.10536 0.48043 4 0.734784 4 1V2H3C2.20435 2 1.44129 2.31607 0.87868 2.87868C0.316071 3.44129 0 4.20435 0 5V16C0 16.7956 0.316071 17.5587 0.87868 18.1213C1.44129 18.6839 2.20435 19 3 19H15C15.7956 19 16.5587 18.6839 17.1213 18.1213C17.6839 17.5587 18 16.7956 18 16V5C18 4.20435 17.6839 3.44129 17.1213 2.87868C16.5587 2.31607 15.7956 2 15 2ZM5 16C4.80222 16 4.60888 15.9414 4.44443 15.8315C4.27998 15.7216 4.15181 15.5654 4.07612 15.3827C4.00043 15.2 3.98063 14.9989 4.01921 14.8049C4.0578 14.6109 4.15304 14.4327 4.29289 14.2929C4.43275 14.153 4.61093 14.0578 4.80491 14.0192C4.99889 13.9806 5.19996 14.0004 5.38268 14.0761C5.56541 14.1518 5.72159 14.28 5.83147 14.4444C5.94135 14.6089 6 14.8022 6 15C6 15.2652 5.89464 15.5196 5.70711 15.7071C5.51957 15.8946 5.26522 16 5 16ZM5 12C4.80222 12 4.60888 11.9414 4.44443 11.8315C4.27998 11.7216 4.15181 11.5654 4.07612 11.3827C4.00043 11.2 3.98063 10.9989 4.01921 10.8049C4.0578 10.6109 4.15304 10.4327 4.29289 10.2929C4.43275 10.153 4.61093 10.0578 4.80491 10.0192C4.99889 9.98063 5.19996 10.0004 5.38268 10.0761C5.56541 10.1518 5.72159 10.28 5.83147 10.4444C5.94135 10.6089 6 10.8022 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12ZM9 16C8.80222 16 8.60888 15.9414 8.44443 15.8315C8.27998 15.7216 8.15181 15.5654 8.07612 15.3827C8.00043 15.2 7.98063 14.9989 8.01921 14.8049C8.0578 14.6109 8.15304 14.4327 8.29289 14.2929C8.43275 14.153 8.61093 14.0578 8.80491 14.0192C8.99889 13.9806 9.19996 14.0004 9.38268 14.0761C9.56541 14.1518 9.72159 14.28 9.83147 14.4444C9.94135 14.6089 10 14.8022 10 15C10 15.2652 9.89464 15.5196 9.70711 15.7071C9.51957 15.8946 9.26522 16 9 16ZM9 12C8.80222 12 8.60888 11.9414 8.44443 11.8315C8.27998 11.7216 8.15181 11.5654 8.07612 11.3827C8.00043 11.2 7.98063 10.9989 8.01921 10.8049C8.0578 10.6109 8.15304 10.4327 8.29289 10.2929C8.43275 10.153 8.61093 10.0578 8.80491 10.0192C8.99889 9.98063 9.19996 10.0004 9.38268 10.0761C9.56541 10.1518 9.72159 10.28 9.83147 10.4444C9.94135 10.6089 10 10.8022 10 11C10 11.2652 9.89464 11.5196 9.70711 11.7071C9.51957 11.8946 9.26522 12 9 12ZM13 12C12.8022 12 12.6089 11.9414 12.4444 11.8315C12.28 11.7216 12.1518 11.5654 12.0761 11.3827C12.0004 11.2 11.9806 10.9989 12.0192 10.8049C12.0578 10.6109 12.153 10.4327 12.2929 10.2929C12.4327 10.153 12.6109 10.0578 12.8049 10.0192C12.9989 9.98063 13.2 10.0004 13.3827 10.0761C13.5654 10.1518 13.7216 10.28 13.8315 10.4444C13.9414 10.6089 14 10.8022 14 11C14 11.2652 13.8946 11.5196 13.7071 11.7071C13.5196 11.8946 13.2652 12 13 12ZM16 8H2V5C2 4.73478 2.10536 4.48043 2.29289 4.29289C2.48043 4.10536 2.73478 4 3 4H4V5C4 5.26522 4.10536 5.51957 4.29289 5.70711C4.48043 5.89464 4.73478 6 5 6C5.26522 6 5.51957 5.89464 5.70711 5.70711C5.89464 5.51957 6 5.26522 6 5V4H12V5C12 5.26522 12.1054 5.51957 12.2929 5.70711C12.4804 5.89464 12.7348 6 13 6C13.2652 6 13.5196 5.89464 13.7071 5.70711C13.8946 5.51957 14 5.26522 14 5V4H15C15.2652 4 15.5196 4.10536 15.7071 4.29289C15.8946 4.48043 16 4.73478 16 5V8Z"
                          strokeWidth="1rem"
                          mask="url(#path-1-inside-1)"
                        />
                        <defs>
                          <linearGradient
                            id="gradient"
                            x1="18"
                            y1="-1.13249e-06"
                            x2="-4.03554"
                            y2="10.6509"
                            gradientUnits="userSpaceOnUse"
                          >
                            <stop stopColor="#590C32" />
                            <stop offset="1" stopColor="#9D1D5A" />
                          </linearGradient>
                        </defs>
                      </svg>
                    </ImageWrapper>
                  ) : (
                    <ClearButton
                      onClick={(e) => {
                        e.stopPropagation()

                        setValue('tripType', 1)
                        setValue('return', null)
                        setSelectingEnd(false)

                        if (isOpen) {
                          setSelectingStart(true)
                        }
                      }}
                    >
                      <ClearIcon />
                    </ClearButton>
                  )}
                </GridItem>
              )}

              {!isMobile && isOpen && tripType !== 2 && (
                <CheckboxSlider
                  checked={isOneWay}
                  onChange={() => {
                    setValue('tripType', isOneWay ? 0 : 1)
                    toggleOneWay(!isOneWay)
                    setSelectingStart(true)
                    setSelectingEnd(false)
                  }}
                />
              )}
            </GridRow>
          </StyledPopupHeader>

          {isOpen && (
            <StyledPopupBody>
              <StyledDatePickerWrapper>
                <DatePicker
                  selected={startDate}
                  onChange={onChangeDay}
                  startDate={tripType === 2 ? null : startDate}
                  endDate={tripType === 2 ? null : endDate}
                  monthsShown={isMobile ? 1 : (tripType === 2 ? 1 : 2)}
                  selectsStart={tripType === 2 ? false : isSelectingStart}
                  selectsEnd={tripType === 2 ? false : isSelectingEnd}
                  shouldCloseOnSelect={tripType === 2}
                  popperPlacement="bottom-center"
                  minDate={new Date()}
                  maxDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                  renderDayContents={renderDayContents}
                  inline
                  fixedHeight
                  showDisabledMonthNavigation
                />
              </StyledDatePickerWrapper>
            </StyledPopupBody>
          )}
        </StyledAbsoluteWrapper>
      </StyledRelativeWrapper>
    </>
  )
}

const StyledRelativeWrapper = styled.div`
  ${({ theme }) => css`
    position: relative;
    width: 100%;
    background: ${theme.colors.white};
    border-radius: ${theme.borderRadius};
    height: 59px;

    @media ${theme.breakpoints.xl} {
    }
  `}
`

const StyledAbsoluteWrapper = styled.div`
  ${({ theme, isOpen, tripType }) => css`
    width: 100%;
    background: ${theme.colors.white};
    /* transition: ${theme.transition}; */
    padding: ${theme.spacing};
    border-radius: ${theme.borderRadius};
    top: 0;
    left: 0;

    ${isOpen &&
    css`
      position: absolute;
      box-shadow: 0px 14px 14px rgba(0, 0, 0, 0.25);
      z-index: 20;
    `}

    @media ${theme.breakpoints.xl} {
      position: absolute;
      ${isOpen &&
      css`
        left: ${tripType === 2 ? '-25%' : '-68%'};
        top: 15%;
        width: ${tripType === 2 ? '30rem' : '53.5rem'};

        margin: -1.5rem;
        padding: 1.5rem;
        padding-bottom: calc(1.5rem + 34px);

        background: #ffffff;
        box-shadow: 0px 14px 14px rgba(0, 0, 0, 0.25);
        border-radius: 30px;

        z-index: 20;
      `}
    }
  `}
`

const StyledPopupHeader = styled.div.attrs({ className: 'popup-header' })`
  ${({ theme, isOpen,withAdditionalPadding }) => css`
    @media ${theme.breakpoints.xl} {
      ${isOpen &&
      css`
        background: #f4f6f7;
        border-radius: 30px 30px 0 0;
        margin: -1.5rem;
        margin-bottom: 0;
        padding: ${withAdditionalPadding ? '8px 1.5rem' : '0 1.5rem'};
        /* padding-left: calc(1.5rem + 190px); */
        /* padding-right: calc(1.5rem + 179px); */
      `}
    }
  `}
`

const StyledPopupBody = styled.div.attrs({ className: 'popup-body' })`
  ${({ theme }) => css`
    display: flex;
    justify-content: center;
  `}
`

const GridRow = styled.div`
  ${({ theme, cols = 2, tripType }) =>
    css`
      display: grid;
      grid-template-columns: ${tripType === 2 ? '1fr' : `repeat(${cols}, 1fr)`};
      align-items: center;
      grid-gap: 0.5rem;

      @media ${theme.breakpoints.xl} {
        grid-template-columns: ${tripType === 2
          ? '1fr'
          : '1fr repeat(3, 175px)'};
        justify-items: center;
      }
    `}
`

const GridItem = styled.div`
  ${({ theme, active, showClearBtn, isMobile, isOpen, tripType, itineraryIndex }) => css`
    cursor: pointer;
    display: grid;
    grid-template-columns: 1fr 2.25rem;
    grid-gap: 0.5rem;
    align-items: center;
    position: relative;
    padding-left: 1rem;
    width: ${tripType === 2 ? '100%' : 'auto'};

    svg {
      fill: #b4b4b4;
    }

    ${active &&
    css`
      label {
        background: linear-gradient(242.97deg, #590c32 0%, #9d1d5a 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      svg {
        fill: url(#gradient);
      }
    `}

    &:first-child:after {
      content: ${({ isOpen }) => (isOpen ? null : '""')};
      width: ${({ tripType }) => (tripType !== 2 ? '1px' : '0px')};
      height: calc(100% + 20px);
      background-color: #eee;
      position: absolute;
      right: -5px;
      top: 0;
      margin: -10px 0;
    }

    @media ${theme.breakpoints.xl} {
      grid-template-columns: ${tripType === 2 ? '1fr 2.25rem' : '125px 2.25rem'};
      padding-left: ${isOpen ? '10px' : '5px'};
      ${active &&
      css`
        background: white;
        height: 5.625rem;
      `}
      &:first-child:after {
        width: ${({ tripType, itineraryIndex }) => (tripType !== 2 || (tripType === 2 && itineraryIndex === 0) ? '1px' : '0px')};
        height: 100%;
        top: 50%;
        transform: translateY(-50%);
        background-color: #e4e4e4;
        margin: 0;
      }
      &:after {
        content: ${({ isOpen }) => (isOpen ? null : '""')};
        width: ${({ tripType, itineraryIndex }) => (tripType !== 2 || (tripType === 2 && itineraryIndex === 0) ? '1px' : '0px')};
        height: 100%;
        background-color: #e4e4e4;
        position: absolute;
        right: -10px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  `}
`

const StyledDateInput = styled.div`
  display: grid;
  grid-template-columns: 1fr;
`
const ImageWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex: none;
  height: 28px;
  height: 1.75rem;
  position: relative;
  width: 28px;
  width: 1.75rem;
  height: 2.25rem;
  width: 2.25rem;
`
const ClearButton = styled.button`
  width: 2.25rem;
  height: 2.25rem;
  background: #b4b4b4;
  align-items: center;
  justify-content: center;
  display: flex;
  border-radius: 50%;

  svg {
    width: 0.875rem;
    height: 0.875rem;
  }
`
