import { useStore } from "effector-react";
import { useEffect, useState, useRef } from 'react'
import { useWatch, useFieldArray } from 'react-hook-form'
import styled, { css } from 'styled-components'

import {
    $activeField,
    setActiveFieldEvent,
} from '../app-state/text-fields'
import { SubmitButton } from '../atoms'
import { breakpoints } from "../constants/breakpoints";
import { useWindowSize } from "../hooks";
import { ReactComponent as PlusIcon } from '../icons/plus.svg'
import { ReactComponent as XIcon } from '../icons/x.svg'

import { DatePickerField } from './date-picker'
import { FormFieldText } from './form-field-text'
import { Passengers } from './passengers'

export const ItinerariesManager = ({
  register,
  control,
  setValue,
  getValues,
  handleSubmit,
  errors
}) => {
  const tripType = useWatch({
    name: 'tripType',
    control
  })

    const watchedItineraries = useWatch({
        name: 'itineraries',
        control
    })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'itineraries'
  })

  const addItinerary = (index) => {
    append({
      from: { label: '', isValid: false },
      to: { label: '', isValid: false },
      departure: null,
      return: null
    })
      setTimeout(() => {
          setValue(`itineraries[${index}].from`, { label: '', isValid: false })
          setValue(`itineraries[${index}].to`, { label: '', isValid: false })
          setValue(`itineraries[${index}].departure`, null)
          setValue(`itineraries[${index}].return`, null)
      }, 0)
  }
    const pendingRestore = useRef([])

    useEffect(() => {
        const current = watchedItineraries ?? []

        if (tripType !== 2 && fields.length > 1) {
            // Cache extra itineraries
            pendingRestore.current = current.slice(1)

            for (let i = fields.length - 1; i > 0; i--) {
                remove(i)
            }

            const first = current?.[0] ?? {
                from: { label: '', isValid: false },
                to: { label: '', isValid: false },
                departure: null,
                return: null
            }

            const cleanFirst = {
                from: { ...first.from },
                to: { ...first.to },
                departure: first?.departure ? new Date(first.departure) : null,
                return: first?.return ? new Date(first.return) : null
            }

            setValue(`itineraries[0].from`, cleanFirst.from)
            setValue(`itineraries[0].to`, cleanFirst.to)
            setValue(`itineraries[0].departure`, cleanFirst.departure)
            setValue(`itineraries[0].return`, cleanFirst.return)
        }

        if (tripType === 2 && pendingRestore.current.length > 0) {
            const restore = pendingRestore.current
            pendingRestore.current = []

            // STEP 1 — Append blank rows
            restore.forEach(() => {
                append({
                    from: { label: '', isValid: false },
                    to: { label: '', isValid: false },
                    departure: null,
                    return: null
                })
            })
            let tries = 0

            // STEP 2 — Wait until fields are registered
            const interval = setInterval(() => {
                const latest = getValues('itineraries')?.length ?? 0
                if (latest >= restore.length + 1 || tries++ > 20) {
                    restore.forEach((item, i) => {
                        const index = i + 1
                        setValue(`itineraries[${index}].from`, item.from)
                        setValue(`itineraries[${index}].to`, item.to)
                        setValue(`itineraries[${index}].departure`, item.departure)
                        setValue(`itineraries[${index}].return`, item.return)
                    })
                    clearInterval(interval)
                }
            }, 10)
        }else if (tripType === 2 && fields.length === 1) {
            append({
                from: { label: '', isValid: false },
                to: { label: '', isValid: false },
                departure: null,
                return: null
            })
            setTimeout(() => {
                setValue(`itineraries[1].from`, { label: '', isValid: false })
                setValue(`itineraries[1].to`, { label: '', isValid: false })
                setValue(`itineraries[1].departure`, null)
                setValue(`itineraries[1].return`, null)
            }, 0)
        }
    }, [tripType, fields.length])

    const switchItineraryCities = (index) => {
        const currentItineraries = getValues('itineraries')
        const itinerary = currentItineraries?.[index]
        if (!itinerary?.from || !itinerary?.to) return

        const fromCity = JSON.parse(JSON.stringify(itinerary.from))
        const toCity = JSON.parse(JSON.stringify(itinerary.to))

        setValue(`itineraries[${index}].from`, toCity, { shouldDirty: true })
        setValue(`itineraries[${index}].to`, fromCity, { shouldDirty: true })
    }

  // Remove itinerary at specific index
  const removeItinerary = (index) => {
    if (fields.length <= 1) return
      remove(index)
    if (fields.length === 2) {
        setValue('tripType', 1)
    }
  }

    const clearAll = () => {
        const current = getValues('itineraries') ?? []

        if (fields.length > 1) {
            pendingRestore.current = []

            for (let i = fields.length - 1; i > 0; i--) {
                remove(i)
            }
        }

        const first = current?.[0] ?? {
            from: { label: '', isValid: false },
            to: { label: '', isValid: false },
            departure: null,
            return: null
        }

        const cleanFirst = {
            from: { ...first.from },
            to: { ...first.to },
            departure: first?.departure ? new Date(first.departure) : null,
            return: first?.return ? new Date(first.return) : null
        }

        setValue(`itineraries[0].from`, cleanFirst.from)
        setValue(`itineraries[0].to`, cleanFirst.to)
        setValue(`itineraries[0].departure`, cleanFirst.departure)
        setValue(`itineraries[0].return`, cleanFirst.return)
    }

  const activeField = useStore($activeField)

  const { width } = useWindowSize()
  const isMobile = width <= breakpoints.xl

  return (
    <>
    <StyledSearchForm data-aos="zoom-in-down" data-aos-easing="ease-out-expo" data-aos-delay="200" data-aos-duration="1500">
      <StyledContainer>
      {fields.map((field, index) => (
        <StyledItineraryItem key={field.id}>
          <StyledCitiesGroup
            shouldLimitWidth={index === 0 && tripType !== 2}>
            <FormFieldText
              inputLabel="from"
              inputId={`search-form-input-from-${index}`}
              inputName={`itineraries[${index}].from`}
              inputPlaceholder="Flying from?"
              inputRegister={register}
              inputType="text"
              setValue={setValue}
              getValues={getValues}
              showSwitch={true}
              control={control}
            />

            <StyledSwitchButton
              hide={Boolean(activeField) && activeField.includes(`itineraries[${index}]`)}
              isMobile={isMobile}
              onClick={() => {
                switchItineraryCities(index)
              }}
            >
              <svg
                className="w-5 h-5 md:w-6 md:h-6 fill-current text-grey-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 23 23"
              >
                <path
                  d="M17.25 3.833l-3.832 3.833h2.875v6.709a1.916 1.916 0 11-3.834 0V7.666a3.838 3.838 0 00-3.833-3.833 3.838 3.838 0 00-3.834 3.833v6.709H1.917l3.834 3.833 3.833-3.833H6.71V7.666a1.916 1.916 0 113.834 0v6.709a3.838 3.838 0 003.833 3.833 3.838 3.838 0 003.833-3.833V7.666h2.875l-3.833-3.833z"
                  fill="#FFF"
                />
              </svg>
            </StyledSwitchButton>

            <FormFieldText
              inputLabel="to"
              inputId={`search-form-input-to-${index}`}
              inputName={`itineraries[${index}].to`}
              inputPlaceholder="Where are you flying?"
              inputRegister={register}
              inputType="text"
              setValue={setValue}
              getValues={getValues}
              showSwitch={false}
              control={control}
            />
          </StyledCitiesGroup>

          <StyledDateGroup>
            <DatePickerField
              setValue={setValue}
              getValues={getValues}
              control={control}
              itineraryIndex={index}
            />
          </StyledDateGroup>

          {index === 0 && (
            <StyledPassengers>
              <Passengers
                {...{
                  inputLabel: 'passengers',
                  inputId: 'search-form-date-return',
                  inputPlaceholder: '1 Passenger',
                  inputRegister: register,
                  inputType: 'text',
                  inputReadOnly: true,
                  register,
                  setValue,
                  getValues,
                  control
                }}
              />
            </StyledPassengers>
          )}

          {tripType !== 2 && (
            <StyledActions>
              <SubmitButton submit={handleSubmit} />
            </StyledActions>
          )}

          {tripType === 2 && fields.length > 1 && index >= 1 && (
            <StyledRemoveButton
              onClick={() => removeItinerary(index)}
              type="button"
            >
              <XIcon />
            </StyledRemoveButton>
          )}
        </StyledItineraryItem>
      ))}
    </StyledContainer>
    </StyledSearchForm>


    {tripType === 2 && (

      <StyledMultiSityActions
          isThreeColumns={fields.length < 8 && fields.length > 2}
      >
        <StyledMultisityFormActions
            isTwoColumns={fields.length < 8 && fields.length > 2}
        >

          <StyledAddButton
              hide={fields.length >= 8}
              onClick={() => addItinerary(fields.length)}
            type="button"
          >
            <PlusIcon/>
            Add a flight
          </StyledAddButton>


          {fields.length > 2 && (
            <StyledClearButton
              onClick={clearAll}
              type="button"
            >
              Clear all
            </StyledClearButton>
          )}
        </StyledMultisityFormActions>

        <StyledSearchButton>
          <SubmitButton submit={handleSubmit} hideIcon={true} />
        </StyledSearchButton>

      </StyledMultiSityActions>

    )}

    </>
  )
}

const StyledSearchForm = styled.div`
  ${({ theme }) => css`
    background-color: transparent;
    position: relative;
    z-index: 20;
    width: 100%;
  `}
`

const StyledContainer = styled.div`
  ${({ theme }) => css`
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing};
    width: 100%;
  `}
`

const StyledItineraryItem = styled.div`
  ${({ theme }) => css`
      display: flex;
      gap: ${theme.spacing};
      width: 100%;
      position: relative;
      border-radius: 99999px;
      padding: 0.25rem;
      flex-direction: row;align-items: center;
      flex-wrap: wrap;
      background-color: transparent;
      @media ${theme.breakpoints.xl} {
          flex-wrap: nowrap;
          background-color: ${theme.colors.white};
      }
  `}
`

const StyledCitiesGroup = styled.div`
  ${({ theme, shouldLimitWidth }) => css`
    width: 100%;
    display: grid;
    background: ${theme.colors.white};
    border-radius: ${theme.borderRadius};
    position: relative;

    @media ${theme.breakpoints.xl} {
      max-width: ${shouldLimitWidth ? '496px': 'unset'};
      grid-template-columns: repeat(2, 1fr);
      flex: 2;
    }
  `}
`

const StyledDateGroup = styled.div`
  ${({ theme }) => css`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    @media ${theme.breakpoints.xl} {
      flex: 1;
      flex-direction: row;
      align-items: center;
    }
  `}
`

const StyledSwitchButton = styled.button`
    ${({ theme, isMobile, hide }) => css`
        cursor: pointer;
        background: ${theme.gradient};
        color: ${theme.colors.white};
        border-radius: ${theme.borderRadiusFull};
        display: flex;
        align-items: center;
        justify-content: center;
        height: 2.25rem;
        width: 2.25rem;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: calc(50% + 10px);

        ${isMobile &&
        css`
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
          z-index: 11;

          visibility: ${hide ? 'hidden' : 'unset'};
        `}

        svg {
            ${!isMobile &&
            css`
                transform: rotate(90deg);
            `}
        }
    `}
`

const StyledAddButton = styled.button`
  ${({ theme, hide }) => css`
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    gap: ${theme.spacing};
    cursor: pointer;
    padding: 0.75rem 1rem;
    background: #fff;
    border-radius: 56px;
    transition: all 0.2s ease;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 110%; /* 13.2px */
    letter-spacing: 1.2px;
    text-transform: uppercase;
    color: #9D1D5A;
    display: ${hide ? 'none' : 'flex'};
      @media ${theme.breakpoints.lg} {
          padding: 0.75rem 1.5rem;
          width: 240px;
          height: 56px;
      }
  `}
`

const StyledRemoveButton = styled.button`
  ${({ theme }) => css`
    color: ${theme.colors.error};
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    border: none;
    position: absolute;
    top: -5px;
    right: 0;
    background-color: ${theme.colors.white};
    border-radius: 99999px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.10);

    display: flex;
    width: 24px;
    height: 24px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.4s ease;

    svg {
      width: 16px;
      height: 16px;
      fill: #71777D;
      stroke: #71777D;
    }

    &:hover {
      svg {
        fill: #9D1D5A;
        stroke: #9D1D5A;
      }
    }
  `}
`

const StyledPassengers = styled.div`
  ${({ theme }) => css`
    width: 100%;
    min-width: 190px;
    background-color: transparent;
    border-radius: 0;
    display: grid;
    grid-gap: ${theme.spacing};

    @media ${theme.breakpoints.xl} {
      grid-template-columns: 1fr;
      grid-gap: 0;
      width: 190px;
    }
  `}
`

const StyledActions = styled.div`
    ${({ theme }) => css`
        flex: none;
        margin-top: 0;
        width: 100%;
        @media ${theme.breakpoints.xl} {
            flex: none;
            margin-top: 0;
            width: 60px;
        }
  `}
`

const StyledMultiSityActions = styled.div`
    ${({ theme, isThreeColumns }) => css`
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        align-items: center;
        gap: ${theme.spacing};
        // padding: ${theme.spacing};
        margin-top: ${theme.spacing};

        @media ${theme.breakpoints.lg} {
            display: grid;
            grid-template-columns: ${isThreeColumns ? '1fr 1fr 1fr' : '1fr 1fr'};
            max-width: 1152px;
        }
        @media ${theme.breakpoints.xl} {
            display: flex;
            flex-direction: row;
        }
    `}
`

const StyledMultisityFormActions = styled.div`
    ${({ theme, isTwoColumns }) => css`
        width: 100%;
        display: grid;
        grid-template-columns: ${isTwoColumns ?  '1fr 1fr': '1fr'};
        align-items: center;
        gap: ${theme.spacing};
      @media ${theme.breakpoints.lg} {
          grid-column: ${isTwoColumns ? 'span 2' : 'span 1'};
          button {
              width: 100%;
          }
      }
        @media ${theme.breakpoints.xl} {
            display: flex;
            width: unset;
            button {
                width: 240px;
            }
        }
  `}
`

const StyledSearchButton = styled.div`
  ${({ theme }) => css`
    width: 100%;
    flex: none;

      @media ${theme.breakpoints.xl} {
          width: 240px;
      }
  `}
`

const StyledClearButton = styled.button`
  ${({ theme }) => css`
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    gap: ${theme.spacing};
    cursor: pointer;
    color: #333333;
    background: #fff;
    border-radius: 56px;
    transition: all 0.2s ease;

    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 110%; /* 13.2px */
    letter-spacing: 1.2px;
    text-transform: uppercase;

    &:active {
      transform: translateY(1px);
    }
      @media ${theme.breakpoints.lg} {
          width: 240px;
          height: 56px;
      }
  `}
`
