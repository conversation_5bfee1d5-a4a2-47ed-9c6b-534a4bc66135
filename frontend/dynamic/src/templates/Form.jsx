import { yupResolver } from '@hookform/resolvers/yup'
import { useEffect } from "react";
import ReactDOM from 'react-dom'
import { useForm } from 'react-hook-form'
import styled, { css } from 'styled-components'

import { ErrorPopup } from '../atoms'
import {
  defaultValues,
  validationSchema
} from '../constants/form-default-values'
import { TripClassSelector, TripTypeSelector } from '../molecules'
import { ItinerariesManager } from '../organisms'

const getDefaultValues = () => {
  try {
    const savedData = JSON.parse(window.localStorage.getItem('form-data'))
    if (
      savedData !== null &&
      savedData.itineraries &&
      savedData.itineraries[0] &&
      new Date(savedData.itineraries[0].departure) >= new Date()
    ) {
      const itineraries = savedData.itineraries.map((itinerary) => ({
        ...itinerary,
        from: {
          ...itinerary.from,
          isValid: Boolean(itinerary.from && itinerary.from.label)
        },
        to: {
          ...itinerary.to,
          isValid: Boolean(itinerary.to && itinerary.to.label)
        },
        departure: itinerary.departure ? new Date(itinerary.departure) : null,
        return: itinerary.return ? new Date(itinerary.return) : null
      }))

      return {
        ...savedData,
        itineraries,
        passengers: {
          adults: savedData.passengers?.adults || 1,
          childrens: savedData.passengers?.childrens || 0,
          infants: savedData.passengers?.infants || 0
        },
        tripClass: savedData.tripClass || 'business',
        tripType: savedData.tripType || 1
      }
    } else {
      return defaultValues
    }
  } catch {
    return defaultValues
  }
}

export const SearchTemplate = () => {
  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    control,
    errors,
    clearErrors,
  } = useForm({
    shouldUnregister: false,
    defaultValues: getDefaultValues(),
    resolver: yupResolver(validationSchema),
    shouldFocusError: false,
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
  })

  return (
    <StyledFormWrapper>
      <TripClassSelector {...{ name: 'tripClass', control }} />

      <TripTypeSelector {...{ name: 'tripType', control }} />

      <ItinerariesManager
        register={register}
        control={control}
        setValue={setValue}
        getValues={getValues}
        handleSubmit={handleSubmit}
        errors={errors}
      />

      {Boolean(Object.keys(errors).length) &&
        ReactDOM.createPortal(
          <ErrorPopup errors={errors} clearErrors={clearErrors} />,
          document.querySelector('body')
        )}
    </StyledFormWrapper>
  )
}

const StyledFormWrapper = styled.div`
  ${({ theme }) => css`
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing};
    align-items: center;
    justify-content: space-around;
    width: 100%;
    max-width: 1152px;

    @media ${theme.breakpoints.md} {
      max-width: 768px;
    }

    @media ${theme.breakpoints.xl} {
      max-width: 1152px;
    }
  `}
`
