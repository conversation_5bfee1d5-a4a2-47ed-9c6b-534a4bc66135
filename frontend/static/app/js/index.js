var customViewportCorrectionVariable = 'vh';
function setViewportProperty(e) {
    var t,
        r = '--' + (customViewportCorrectionVariable || 'vh');
    function o() {
        var o = e.clientHeight;
        o !== t &&
            requestAnimationFrame(function () {
                e.style.setProperty(r, 0.01 * o + 'px'), (t = o);
            });
    }
    return o(), o;
}
window.addEventListener('resize', setViewportProperty(document.documentElement));
import 'swiper/swiper-bundle.css';
import 'aos/dist/aos.css';

const articlesCarousel = document.querySelectorAll('.articles-carousel');
const reviewCarousel = document.querySelectorAll('.review-carousel');
const corpPartnersCarousel = document.querySelectorAll('.corp-partners-carousel');
const corpFeedbacksCarousel = document.querySelectorAll('.corp-feedbacks-carousel');

const aosSelector = document.querySelector('[data-aos]');
const videoBG = document.getElementById('video-bg');

if (videoBG) {
    const cdn = videoBG?.getAttribute('data-cdn') || '../';
    function videoBGLoad() {
        ['webm', 'mp4'].forEach((type) => {
            // const video = require(`${cdn}/img/video/background.${type}`);
            const source = document.createElement('source');
            source.src = `${cdn}/img/video/background.${type}`;
            source.type = `video/${type}`;

            videoBG.appendChild(source);
        });
    }

    if (document.readyState == 'complete') {
        videoBGLoad();
    } else {
        if (window.attachEvent) {
            window.attachEvent('onload', videoBGLoad);
        } else {
            window.addEventListener('load', videoBGLoad, false);
        }
    }
}

import GDPR from './GDPR';
import modalBonus from './modal-bonus';
import modalBlackFriday from './modal-black-friday';
import utm from './utm';

window.GDPR = function () {
    return {
        GDPR: {
            isOpen: false,
            init() {
                try {
                    const accept = window.localStorage.getItem('GDPR_ACCEPT') || false;

                    if (!accept) {
                        if (
                            Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone.includes('Europe')
                        ) {
                            this.isOpen = true;
                        } else {
                            this.onAccept();
                        }
                    }
                } catch {
                    this.onAccept();
                }
            },
            onAccept() {
                try {
                    window.localStorage.setItem('GDPR_ACCEPT', 1);
                } finally {
                    this.isOpen = false;
                }
            },
        },
    };
};

import(/* webpackChunkName: "alpinejs" */ 'alpinejs').then(({ default: Alpine }) => {
    window.Alpine = Alpine;

    Alpine.data('dropdownMenu', () => ({
        open: false,

        trigger: {
            ['@click.throttle']() {
                this.open = true;
            },
        },

        dialogue: {
            ['x-show']() {
                return this.open;
            },
            ['@click.outside.throttle']() {
                this.open = false;
            },
            'x-transition:enter': 'transition ease-in-out-sine duration-200 delay-50',
            'x-transition:enter-start': 'opacity-0 translate-y-2',
            'x-transition:enter-end': 'opacity-100 translate-y-0',
            'x-transition:leave': 'transition ease-out duration-200 delay-0',
            'x-transition:leave-start': 'opacity-100 translate-y-0',
            'x-transition:leave-end': 'opacity-0 translate-y-2',
        },

        item: {
            ['@click.throttle']() {
                this.open = false;
            }
        }
    }));

    Alpine.start();
});

if (aosSelector) {
    import(/* webpackChunkName: "aos" */ 'aos').then(({ default: AOS }) => {
        window.AOS = AOS;
        AOS.init({
            once: true,
            disableMutationObserver: true,
            mirror: true,
        });
    });
}

if ((articlesCarousel && articlesCarousel.length) || (reviewCarousel && reviewCarousel.length) || (corpPartnersCarousel && corpPartnersCarousel.length) || (corpFeedbacksCarousel && corpFeedbacksCarousel.length)) {
    import(/* webpackChunkName: "swiper" */ 'swiper/bundle').then(({ default: Swiper }) => {
        [...articlesCarousel].forEach((el) => {
            new Swiper(el, {
                slidesPerView: 1,
                spaceBetween: 10,
                roundLengths: true,
                setWrapperSize: true,
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    1280: {
                        slidesPerView: 3,
                        spaceBetween: 60,
                    },
                    1536: {
                        slidesPerView: 3,
                        spaceBetween: 60,
                    },
                },
                navigation: {
                    nextEl: '.articles-carousel-next',
                    prevEl: '.articles-carousel-prev',
                },
            });
        });

        [...reviewCarousel].forEach((el) => {
            new Swiper(el, {
                slidesPerView: 1,
                roundLengths: true,
                spaceBetween: 10,
                watchSlidesVisibility: true,
                watchSlidesProgress: true,
                speed: 1300,
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 60,
                        slidesPerGroup: 2,
                    },
                    1024: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                        slidesPerGroup: 2,
                    },
                    1280: {
                        slidesPerView: 3,
                        spaceBetween: 60,
                        slidesPerGroup: 3,
                    },
                    1536: {
                        slidesPerView: 4,
                        spaceBetween: 60,
                        slidesPerGroup: 4,
                    },
                },
                pauseOnMouseEnter: true,
                autoplay: {
                    delay: 8000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '.review-carousel-next',
                    prevEl: '.review-carousel-prev',
                },
                pagination: {
                    el: '.tripadvisor-carousel-pagination',
                    clickable: false,
                },
            });
        });

        document.querySelectorAll('.corp-partners-carousel').forEach((el) => {
            const paginationEl = el.querySelector('.corp-partners-carousel-pagination');

            new Swiper(el, {
                slidesPerView: 2,
                slidesPerGroup: 2,
                roundLengths: true,
                spaceBetween: 10,
                watchSlidesVisibility: true,
                watchSlidesProgress: true,
                speed: 1300,
                breakpoints: {
                    640: {
                        slidesPerView: 4,
                        spaceBetween: 40,
                        slidesPerGroup: 4,
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 20,
                        slidesPerGroup: 5,
                    },
                    1280: {
                        slidesPerView: 5,
                        spaceBetween: 100,
                        slidesPerGroup: 5,
                    },
                    1536: {
                        slidesPerView: 5,
                        spaceBetween: 100,
                        slidesPerGroup: 5,
                    },
                    1920: {
                        slidesPerView: 5,
                        spaceBetween: 100,
                        slidesPerGroup: 5,
                    }
                },
                pauseOnMouseEnter: true,
                autoplay: {
                    delay: 8000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: paginationEl,
                    clickable: true, // set to true to see interaction
                },
            });
        });


        document.querySelectorAll('.corp-feedbacks-carousel').forEach((el) => {
            const paginationEl = el.querySelector('.corp-feedbacks-carousel-pagination');

            new Swiper(el, {
                slidesPerView: 1,
                roundLengths: true,
                spaceBetween: 10,
                watchSlidesVisibility: true,
                watchSlidesProgress: true,
                speed: 1300,
                breakpoints: {
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 80,
                        slidesPerGroup: 2,
                    },
                    1280: {
                        slidesPerView: 3,
                        spaceBetween: 84,
                        slidesPerGroup: 3,
                    },
                    1920: {
                        slidesPerView: 3,
                        spaceBetween: 61,
                        slidesPerGroup: 3,
                    }
                },
                pauseOnMouseEnter: true,
                autoplay: {
                    delay: 8000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: paginationEl,
                    clickable: false,
                },
            });
        });
    });
}

window.modalBonusData = () => {
    return {
        modalBonus: modalBonus,
    };
};

window.modalBlackFriday = () => {
    return {
        modalBlackFriday: modalBlackFriday,
    };
};
