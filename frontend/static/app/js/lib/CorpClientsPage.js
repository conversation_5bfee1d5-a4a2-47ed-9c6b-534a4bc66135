import api from "../api/api"
import $ from "jquery"

export default class CorpClientsPage {
    constructor(pageType = 'result') {
        this.pageType = pageType
        this.completeUrl = '/corp-clients/create'
        this.form = document.querySelector('#form-container')
        this.submitButton = this.form?.querySelector('button[type="submit"]')
        this.successUrl = '/corp-clients/success'

        // New class-level elements
        this.scrollButtons = document.querySelectorAll('.get-corporate-offer')
        this.formBlock = document.getElementById('formBlock')
    }

    init() {
        if (this.form) {
            this.form.addEventListener('submit', this.handleFormSubmit.bind(this))
        }

        this.bindScrollToForm()
    }

    bindScrollToForm() {
        if (this.scrollButtons && this.formBlock) {
            this.scrollButtons.forEach((btn) => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault()
                    this.formBlock.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                    })
                })
            })
        }
    }

    validate() {
        let isValid = true

        const fields = {
            fullName: $('#fullName'),
            email: $('#email'),
            phone: $('#phone'),
            company: $('#company'),
            travellers: $('#travellers'),
        }

        Object.entries(fields).forEach(([key, $input]) => {
            const value = $input.val()?.trim()
            const type = $input.attr('type')
            const min = parseFloat($input.attr('min'))
            const $wrapper = $input.closest('.form-field')

            let inputValid = !!value

            if (type === 'number' && inputValid) {
                const numVal = parseFloat(value)
                if (!isFinite(numVal) || (min && numVal < min)) {
                    inputValid = false
                }
            }

            if (!inputValid) {
                isValid = false
                $wrapper.addClass('!border-[#EB5757]')
                $input.siblings('.error-message').removeClass('hidden')
            } else {
                $wrapper.removeClass('!border-[#EB5757]')
                $input.siblings('.error-message').addClass('hidden')
            }
        })

        return isValid
    }

    async handleFormSubmit(event) {
        event.preventDefault()

        if (!this.validate()) {
            console.warn('❌ Validation failed.')
            return
        }

        const data = {
            fullName: $('#fullName').val()?.trim(),
            email: $('#email').val()?.trim(),
            phone: $('#phone').val()?.trim(),
            company: $('#company').val()?.trim(),
            travellers: $('#travellers').val()?.trim(),
        }

        console.log('✅ Form is valid. Data:', data)

        this.submitButton.disabled = true
        this.submitButton.classList.add('opacity-50', 'cursor-not-allowed')

        try {
            await api.post(this.completeUrl, data)
            window.location.href = this.successUrl
        } catch (error) {
            alert('Something went wrong. Please try again later.')
        } finally {
            this.submitButton.disabled = false
            this.submitButton.classList.remove('opacity-50', 'cursor-not-allowed')
        }
    }
}
