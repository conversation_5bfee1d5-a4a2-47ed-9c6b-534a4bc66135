/**
 * This injects any component classes registered by plugins.
 *
 */
@import 'tailwindcss/components';

/**
 * Here we add custom component classes; stuff we want loaded
 * *before* the utilities so that the utilities can still
 * override them.
 *
 */

@import 'components/article';
@import 'components/autocomplete';
@import 'components/results-autocomplete';
@import 'components/buttons';
@import 'components/datepicker';
@import 'components/results-datepicker';
@import 'components/dropdown';
@import 'components/intlTelInput';
@import 'components/form';
@import 'components/modal';
@import 'components/menu-dropdown';
@import 'components/nav';
@import 'components/passengers';
@import 'components/pagination';
@import 'components/region-filter';
@import 'components/results-passengers';
@import 'components/reviews-carousel';
@import 'components/corp-partners-carousel';
@import 'components/corp-feedbacks-carousel';
@import 'components/promo-autocomplete';
@import 'components/promo-datepicker';
@import 'components/promo-passengers';
@import 'components/steps';
@import 'components/timer';
@import 'components/input-switch';

/**
 * Include styles for individual layouts
 *
 */

@import 'layouts/tickets-search';
@import 'layouts/mobile-tickets-search';
@import 'layouts/hero-container';
@import 'layouts/fly-scene';
@import 'layouts/promo-flights';
@import 'layouts/offer-success';

/**
 * Include styles for individual pages
 *
 */

