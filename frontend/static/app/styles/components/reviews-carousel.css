.tripadvisor-carousel {
    @apply relative px-6  mt-16 lg:px-20 lg:pb-10;

    .swiper-slide {
        @apply
            relative
            select-none
            before:hidden
            md:before:block
            before:absolute
            before:-left-8
            before:top-0
            before:w-px
            before:h-32
            before:bg-grey-300;
    }

    .swiper-slide-reset-before::before {
        content: none !important;
        display: none !important;
    }

    .review-carousel {
        &-prev,
        &-next {
            @apply
                flex
                items-center
                justify-center
                absolute
                top-0
                lg:top-1/2
                w-11
                h-11
                lg:-mt-12
                select-none
                rounded-full
                text-white
                bg-gradient-to-r
                from-pink-light
                to-pink-dark
                z-10
                lg:-translate-y-1/2;

            &.swiper-button-disabled {
                @apply bg-none bg-grey-300 pointer-events-none text-grey-700;
            }
        }

        &-prev {
            @apply left-6 lg:left-0;
        }

        &-next {
            @apply right-6 lg:right-0;
        }
    }

    &-pagination {
        @apply relative flex items-center justify-center space-x-2 max-w-sm mt-6 mx-auto md:mt-8 xl:mt-12;

        .swiper-pagination-bullet {
            @apply
                relative
                flex-1
                w-auto
                h-1
                rounded-xl
                overflow-hidden
                opacity-100
                bg-grey-600/20
                before:absolute
                before:inset-0
                before:origin-left;
        }

        .swiper-pagination-bullet-active {
            &:before {
                @apply bg-grey-600;
                animation: slide-progress 8s linear forwards;
            }
        }
    }
}

@keyframes slide-progress {
    0% {
        transform: scaleX(0);
    }

    100% {
        transform: scaleX(1);
    }
}
