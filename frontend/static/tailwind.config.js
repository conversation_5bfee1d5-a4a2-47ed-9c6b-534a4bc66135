const plugin = require('tailwindcss/plugin');

var env = process.env.NODE_ENV;

module.exports = {
    mode: 'jit',
    purge: {
        preserveHtmlElements: false,
        content: [
            './app/templates/**/*.twig',
            './app/**/*.twig',
            './app/js/**/*.js'
        ],
    },
    darkMode: false,
    theme: {
        fill: {
            current: 'currentColor',
            white: 'white',
            black: '#333',
            gradient: 'url(#lgrad)'
        },
        stroke: {
            current: 'currentColor',
            white: 'white',
            black: '#333',
            gradient: 'url(#lgrad)'
        },
        colors: {
            transparent: 'transparent',
            current: 'currentColor',
            black: 'black',
            white: 'white',
            blueGrey: {
                '700': '#374657',
                '800': '#282F36'
            },
            grey: {
                '100': '#F1F1F1',
                '200': '#F4F6F7',
                '300': '#E4E4E4',
                '400': '#B4B4B4',
                '500': '#838D95',
                '600': '#71777D',
                '700': '#333333',
                '800': '#061620',
                '900': '#D7D7D7'
            },
            green: '#00C908',
            pink: {
                '50': '#FFF1EE',
                '100': '#E8DAD7',
                dark: '#590C32',
                light: '#9D1D5A',
                'dark-transparent': 'rgba(62, 10, 35, 0.85)'
            },
            blue: {
                '450': '#4A94EC'
            },
            deepOrange: {
                '300': '#FF6D4D'
            },
            red: {
                '150': '#FF3434',
                '350': '#E42B50'
            },
            purple: {
                '450': '#6F00C6'
            },
            violet: {
                dark: '#470865'
            },
            primary: 'var(--color-primary)',
            secondary: 'var(--color-secondary)',
            success: 'var(--color-success)',
            info: 'var(--color-info)',
            warning: 'var(--color-warning)',
            danger: 'var(--color-danger)',
            light: 'var(--color-light)',
            dark: 'var(--color-dark)'
        },
        fontFamily: {
            'sans': ['sbc-font', 'sans-serif'],
            'body': ['sbc-font', 'sans-serif']
        },
        fontWeight: {
            normal: 'normal',
            bold: 'bold'
        },
        extend: {
            screens: {
                'hd': '1920px',
            },
            padding: {
                '13': '3.25rem',
                '15': '3.75rem',
                '17': '4.25rem',
                '18': '4.5rem'
            },
            borderRadius: {
                '3xl': '1.25rem',
                '4xl': '1.5rem',
                '5xl': '1.75rem',
                '6xl': '2rem',
                '7xl': '2.25rem',
                '8xl': '2.5rem',
                '9xl': '2.75rem',
                '10xl': '3rem',
                '11xl': '3.25rem',
                '12xl': '3.5rem',
                '13xl': '3.75rem',
                '14xl': '4rem',
                '15xl': '4.25rem',
                '16xl': '4.5rem',
                '17xl': '4.75rem',
                '18xl': '5rem',
                '19xl': '5.25rem',
                '20xl': '5.5rem',
                '21xl': '5.75rem',
                '22xl': '6rem',
                '23xl': '6.25rem',
                '24xl': '6.5rem',
                '25xl': '6.75rem'
            },
            transitionProperty: {
                'height': 'height',
                'spacing': 'margin, padding'
            },
            transitionTimingFunction: {
                'in-sine': 'cubic-bezier(0.12, 0, 0.39, 0)',
                'out-sine': 'cubic-bezier(0.61, 1, 0.88, 1)',
                'in-out-sine': 'cubic-bezier(0.37, 0, 0.63, 1)',
                'in-cubic': 'cubic-bezier(0.32, 0, 0.67, 0)',
                'out-cubic': 'cubic-bezier(0.33, 1, 0.68, 1)',
                'in-out-cubic': 'cubic-bezier(0.65, 0, 0.35, 1)',
                'in-quint': 'cubic-bezier(0.64, 0, 0.78, 0)',
                'out-quint': 'cubic-bezier(0.22, 1, 0.36, 1)',
                'in-out-quint': 'cubic-bezier(0.83, 0, 0.17, 1)',
                'in-circ': 'cubic-bezier(0.55, 0, 1, 0.45)',
                'out-circ': 'cubic-bezier(0, 0.55, 0.45, 1)',
                'in-out-circ': 'cubic-bezier(0.85, 0, 0.15, 1)',
                'in-quad': 'cubic-bezier(0.11, 0, 0.5, 0)',
                'out-quad': 'cubic-bezier(0.5, 1, 0.89, 1)',
                'in-out-quad': 'cubic-bezier(0.45, 0, 0.55, 1)',
                'in-quart': 'cubic-bezier(0.5, 0, 0.75, 0)',
                'out-quart': 'cubic-bezier(0.25, 1, 0.5, 1)',
                'in-out-quart': 'cubic-bezier(0.76, 0, 0.24, 1)',
                'in-expo': 'cubic-bezier(0.7, 0, 0.84, 0)',
                'out-expo': 'cubic-bezier(0.16, 1, 0.3, 1)',
                'in-out-expo': 'cubic-bezier(0.87, 0, 0.13, 1)',
                'in-back': 'cubic-bezier(0.36, 0, 0.66, -0.56)',
                'out-back': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                'in-out-back': 'cubic-bezier(0.68, -0.6, 0.32, 1.6)',
            },
            transitionDuration: {
                '250': '250ms',
                '350': '350ms',
                '400': '400ms',
                '450': '450ms',
                '550': '550ms',
                '600': '600ms',
            },
            transitionDelay: {
                '0': '0ms',
                '25': '25ms',
                '50': '50ms',
                '75': '75ms',
                '100': '100ms',
                '125': '125ms',
                '150': '150ms',
                '175': '175ms',
                '200': '200ms',
                '225': '225ms',
                '250': '250ms',
                '275': '275ms',
                '300': '325ms'
            },
            fontSize: {
                '3xs': '0.625rem',
                '2xs': '0.6875rem',
                'lead': '1.125rem'
            },
            maxWidth: {
                '1/4': '25%',
                '1/2': '50%',
                '3/4': '75%',
                '8xl': '88rem',
                '8.5xl': '92rem',
                '9xl': '96rem'
            },
            borderWidth: {
                '3': '3px',
                '11': '11px'
            },
            boxShadow: {
                'inset-2xl': 'inset 0px 44px 34px rgba(0, 0, 0, 0.06)',
                'inset-light': 'inset 0px 0px 4px rgba(0, 0, 0, 0.19)',
                'light': '0 14px 14px rgba(0,0,0,.05)',
                'light-hard': '0 0 24px 14px rgba(0,0,0,.05)',
                'dark': '0 0 24px 10px rgba(0, 0, 0, .25)',
                'dark-light': '0 0 14px 14px rgba(0,0,0,.25)',
                'blue-light': '0px 4px 15px rgba(68, 85, 234, 0.1)'
            },
            width: {
                '13': '3.25rem',
                '15': '3.75rem',
                '17': '4.25rem'
            },
            height: {
                '13': '3.25rem',
                '15': '3.75rem',
                '17': '4.25rem'
            },
            minHeight: {
                'full-with-header': 'calc(100vh - 120px)',
                '104': '28rem'
            },
            gridTemplateColumns: {
                '13': 'repeat(13, minmax(0, 1fr))',
                '14': 'repeat(14, minmax(0, 1fr))',
                '15': 'repeat(15, minmax(0, 1fr))',
                '16': 'repeat(16, minmax(0, 1fr))',
                '17': 'repeat(17, minmax(0, 1fr))',
                '19': 'repeat(19, minmax(0, 1fr))',
                '29': 'repeat(29, minmax(0, 1fr))',
                '30': 'repeat(30, minmax(0, 1fr))',
            },
            backgroundImage: {
                'stripped-line-y': "url('/img/svg/line-bg.svg')",
                'stripped-line-x': "url('/img/svg/pattern-x.svg')",
                'stripped-line-y-white': "url('/img/svg/line-bg-white.svg')",
                'stripped-line-x-white': "url('/img/svg/pattern-x-white.svg')",
                'radial-shadow': "url('/img/svg/radial-shadow.svg')",
                'hero-contacts': "url('/img/static/contacts-bg.png')",
                'hero-about': "url('/img/static/hero-about.png')",
                'hero-offer': "url('/img/static/hero-offer.png')",
                'about-values': "url('/img/static/about-values-bg.png')",
                'covid-1': "url('/img/static/covid-bg-1.jpg')",
                'covid-2': "url('/img/static/covid-bg-2.jpg')",
                'covid-3': "url('/img/static/covid-bg-3.jpg')",
                'modal-offer': "url('/img/static/modal-offer-bg.png')",
                'brick-wall': "url('/img/static/brickwall.png')"
            },
            backgroundSize: {
                '6px': '6px',
                '100%': '100%'
            },
            margin: {
                '-15': '-3.75rem'
            },
            gap: {
                '15': '3.75rem'
            },
            inset: {
                '13': '3.25rem'
            },
            zIndex: {
                '-1' : '-1',
                '-10': '-10',
                '1': '1',
                '5': '5'
            },
            gridRow: {
             'span-8': 'span 8 / span 8',
            },
        }
    },
    variants: {
        extend: {
            backgroundImage: ['hover', 'group-hover'],
            fill: ['hover', 'group-hover'],
            height: ['opened'],
            width: ['opened'],
            backgroundColor: ['opened', 'checked-custom'],
            backgroundOpacity: ['opened'],
            padding: ['opened', 'group-hover'],
            transform: ['opened', 'group-hover'],
            translate: ['opened', 'group-hover'],
            scale: ['opened'],
            rotate: ['opened'],
            opacity: ['opened'],
            boxShadow: ['opened', 'group-hover'],
            transformOrigin: ['opened'],
            textColor: ['active', 'opened', 'checked-custom'],
            borderRadius: ['opened', 'first', 'last'],
            stroke: ['hover', 'group-hover'],
            display: ['first'],
            outline: ['focus'],
            margin: ['last']
        }
    },
    plugins: [
        require('@tailwindcss/aspect-ratio'),
        require('@tailwindcss/typography'),
        plugin(function({ addVariant, e }) {
            addVariant('opened', ({ modifySelectors, separator }) => {
                modifySelectors(({ className }) => {
                    return `.opened .${e(`opened${separator}${className}`)}`
                })
            })
        }),
        plugin(function({ addVariant, e }) {
            addVariant('checked-custom', ({ modifySelectors, separator }) => {
                modifySelectors(({ className }) => {
                    return `.custom:checked + .${e(`custom-checked${separator}${className}`)}`
                })
            })
        })
    ],
}
