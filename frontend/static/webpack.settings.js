
module.exports = {
    paths: {
        src: {
            base: './app/',
            css: './app/styles/',
            js: './app/js/',
            images: './app/img/',
            fonts: './app/fonts/',
            templates: './app/templates/'
        },
        dist: {  // Complete build folder ( End point)
            base: './dist/' ,
            assets: './dist/build/',
            js: 'js/',
            css: 'css/',
            images: 'img/',
            fonts: 'fonts/',
            templates: '../',
            clean: [
                '**/*'
            ]
        },
        publicPath: {
            base: './build/'
        }
    },
    entry: {
        'waiting': '@/js/waiting.js',
        'result': '@/js/result-page.js',
        'deal': '@/js/best-deal.js',
        'offer': '@/js/offer-page.js',
        'review': '@/js/review.js',
        'discounted': '@/js/discounted-deals.js',
        'corp': '@/js/corp-clients.js',
        'trustpilot': '@/js/trustpilot.js',
        'blog': '@/js/blog.js',
        'contacts': '@/js/contacts.js',
        'app': [
            '@/js/index.js',
            '@/js/top-deals.js',
            '@/js/review.js',
            '@/js/subscribe-newsletters.js',
            '@/js/covid-modal-window.js',
            '@/styles/app-base.css',
            '@/styles/app-components.css',
            '@/styles/app-utilities.css',
        ],

    },
    babelLoaderConfig: {
        exclude: [
            /(node_modules)/
        ],
    },
    gzipEnabled: false,
    brotliEnabled: false
}
